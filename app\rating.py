"""
Hardened Rating/Vote API
Implements secure rating system with:
- One vote per real fingerprint
- Ban enforcement
- Nonce validation
- Captcha integration support
"""

from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, abort
from app.models import get_db
from app.utils.common import get_client_ip
from app.fp import <PERSON>ceManager, FingerprintManager

rating_bp = Blueprint('rating', __name__)

class SecureRatingManager:
    """Manages secure rating operations with fingerprint validation"""
    
    @staticmethod
    def validate_rating_request(data):
        """Validate rating request data"""
        required_fields = ['item_id', 'rating', 'nonce']
        missing_fields = [field for field in required_fields if field not in data]
        
        if missing_fields:
            return False, f"Missing required fields: {', '.join(missing_fields)}"
        
        # Validate rating value
        rating = data.get('rating')
        if not isinstance(rating, (int, float)) or rating < 1 or rating > 5:
            return False, "Rating must be between 1 and 5"
        
        # Validate item_id
        item_id = data.get('item_id')
        if not isinstance(item_id, int) or item_id <= 0:
            return False, "Invalid item_id"
        
        return True, "Valid rating request"
    
    @staticmethod
    def check_existing_rating_with_cursor(cursor, item_id, fingerprint):
        """Check if fingerprint has already rated this item (using existing cursor)"""
        cursor.execute('''
            SELECT id, rating, created_at FROM ratings
            WHERE item_id = ? AND fingerprint = ?
        ''', (item_id, fingerprint))

        result = cursor.fetchone()
        if result:
            return {
                'exists': True,
                'rating_id': result[0],
                'current_rating': result[1],
                'created_at': result[2]
            }
        return {'exists': False}

    @staticmethod
    def check_existing_rating(item_id, fingerprint):
        """Check if fingerprint has already rated this item"""
        with get_db() as conn:
            cursor = conn.cursor()
            return SecureRatingManager.check_existing_rating_with_cursor(cursor, item_id, fingerprint)
    
    @staticmethod
    def submit_rating(item_id, fingerprint, rating, review=None, ip_address=None, user_agent=None):
        """Submit or update a rating"""
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Check if rating already exists
            existing = SecureRatingManager.check_existing_rating(item_id, fingerprint)
            
            if existing['exists']:
                # Update existing rating
                cursor.execute('''
                    UPDATE ratings 
                    SET rating = ?, review = ?, updated_at = CURRENT_TIMESTAMP,
                        ip_address = ?, user_agent = ?
                    WHERE item_id = ? AND fingerprint = ?
                ''', (rating, review, ip_address, user_agent, item_id, fingerprint))
                
                # Log the rating change
                SecureRatingManager.log_rating_event(
                    existing['rating_id'], 'updated', 
                    f"Rating changed from {existing['current_rating']} to {rating}",
                    fingerprint, ip_address
                )
                
                action = 'updated'
                rating_id = existing['rating_id']
            else:
                # Create new rating
                cursor.execute('''
                    INSERT INTO ratings (item_id, fingerprint, rating, review, ip_address, user_agent)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (item_id, fingerprint, rating, review, ip_address, user_agent))
                
                rating_id = cursor.lastrowid
                
                # Log the new rating
                SecureRatingManager.log_rating_event(
                    rating_id, 'created', f"New rating: {rating}",
                    fingerprint, ip_address
                )
                
                action = 'created'
            
            # Update item's average rating cache
            SecureRatingManager.update_item_rating_cache(item_id)
            
            conn.commit()
            return True, f"Rating {action} successfully", rating_id
    
    @staticmethod
    def update_item_rating_cache(item_id):
        """Update cached average rating for an item"""
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Calculate new average
            cursor.execute('''
                SELECT AVG(rating), COUNT(*) FROM ratings WHERE item_id = ?
            ''', (item_id,))
            result = cursor.fetchone()
            
            avg_rating = round(result[0], 2) if result[0] else 0.0
            rating_count = result[1] if result[1] else 0
            
            # Update the apps table (assuming ratings are for apps)
            cursor.execute('''
                UPDATE apps SET rating = ?, rating_count = ? WHERE id = ?
            ''', (avg_rating, rating_count, item_id))
            
            conn.commit()
            return avg_rating, rating_count
    
    @staticmethod
    def log_rating_event(rating_id, event_type, description, fingerprint, ip_address):
        """Log rating-related events for audit trail"""
        with get_db() as conn:
            cursor = conn.cursor()

            # Table is created by models.py
            
            cursor.execute('''
                INSERT INTO rating_events (rating_id, event_type, description, fingerprint, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (rating_id, event_type, description, fingerprint, ip_address, 
                  request.headers.get('User-Agent', '') if request else ''))
            
            conn.commit()
    
    @staticmethod
    def get_item_ratings(item_id, limit=50, offset=0):
        """Get ratings for a specific item"""
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Get total count
            cursor.execute('SELECT COUNT(*) FROM ratings WHERE item_id = ?', (item_id,))
            total = cursor.fetchone()[0]
            
            # Get paginated ratings
            cursor.execute('''
                SELECT rating, review, created_at, updated_at
                FROM ratings 
                WHERE item_id = ?
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            ''', (item_id, limit, offset))
            
            ratings = []
            for row in cursor.fetchall():
                ratings.append({
                    'rating': row[0],
                    'review': row[1],
                    'created_at': row[2],
                    'updated_at': row[3]
                })
            
            return {
                'ratings': ratings,
                'total': total,
                'limit': limit,
                'offset': offset
            }
    
    @staticmethod
    def get_rating_statistics(item_id):
        """Get detailed rating statistics for an item"""
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Get rating distribution
            cursor.execute('''
                SELECT rating, COUNT(*) as count
                FROM ratings 
                WHERE item_id = ?
                GROUP BY rating
                ORDER BY rating DESC
            ''', (item_id,))
            
            distribution = {}
            total_ratings = 0
            for row in cursor.fetchall():
                distribution[int(row[0])] = row[1]
                total_ratings += row[1]
            
            # Calculate average
            cursor.execute('SELECT AVG(rating) FROM ratings WHERE item_id = ?', (item_id,))
            avg_rating = cursor.fetchone()[0] or 0.0
            
            return {
                'item_id': item_id,
                'average_rating': round(avg_rating, 2),
                'total_ratings': total_ratings,
                'distribution': distribution
            }

# API Routes
@rating_bp.route('/api/rate/<int:item_id>', methods=['POST'])
def api_rate_item(item_id):
    """API endpoint to rate an item"""
    try:
        # Only accept JSON content
        if not request.is_json:
            abort(400, "Content-Type must be application/json")
        
        data = request.get_json()
        if not data:
            abort(400, "Invalid JSON data")
        
        # Add item_id to data
        data['item_id'] = item_id
        
        # Validate request data
        is_valid, message = SecureRatingManager.validate_rating_request(data)
        if not is_valid:
            return jsonify({'success': False, 'error': message}), 400
        
        # Get and validate fingerprint
        fingerprint = request.cookies.get('fingerprint') or request.headers.get('X-Visitor-FP')
        if not fingerprint:
            return jsonify({'success': False, 'error': 'Valid fingerprint required'}), 400
        
        # Check if fingerprint is banned
        if FingerprintManager.is_fingerprint_banned(fingerprint):
            abort(403, "Access denied")
        
        # Validate nonce
        nonce = data.get('nonce')
        nonce_valid, nonce_message = NonceManager.validate_and_consume_nonce(nonce, fingerprint)
        if not nonce_valid:
            return jsonify({'success': False, 'error': nonce_message}), 400
        
        # Verify fingerprint exists in system
        fp_data = FingerprintManager.get_fingerprint_data(fingerprint)
        if not fp_data:
            return jsonify({'success': False, 'error': 'Invalid fingerprint'}), 400
        
        # Submit rating
        rating = data.get('rating')
        review = data.get('review', '').strip()
        ip_address = get_client_ip()
        user_agent = request.headers.get('User-Agent', '')
        
        success, message, rating_id = SecureRatingManager.submit_rating(
            item_id, fingerprint, rating, review, ip_address, user_agent
        )
        
        if success:
            # Get updated statistics
            stats = SecureRatingManager.get_rating_statistics(item_id)
            
            return jsonify({
                'success': True,
                'message': message,
                'rating_id': rating_id,
                'statistics': stats
            })
        else:
            return jsonify({'success': False, 'error': message}), 400
            
    except Exception as e:
        return jsonify({'success': False, 'error': 'Internal server error'}), 500

@rating_bp.route('/api/ratings/<int:item_id>')
def api_get_ratings(item_id):
    """API endpoint to get ratings for an item"""
    page = request.args.get('page', 1, type=int)
    per_page = min(request.args.get('per_page', 20, type=int), 100)  # Max 100 per page
    offset = (page - 1) * per_page
    
    ratings_data = SecureRatingManager.get_item_ratings(item_id, per_page, offset)
    stats = SecureRatingManager.get_rating_statistics(item_id)
    
    return jsonify({
        'success': True,
        'ratings': ratings_data,
        'statistics': stats
    })

@rating_bp.route('/api/rating-stats/<int:item_id>')
def api_rating_statistics(item_id):
    """API endpoint to get rating statistics only"""
    stats = SecureRatingManager.get_rating_statistics(item_id)
    return jsonify({
        'success': True,
        'statistics': stats
    })

@rating_bp.route('/api/my-rating/<int:item_id>')
def api_get_my_rating(item_id):
    """API endpoint to get current user's rating for an item"""
    fingerprint = request.cookies.get('fingerprint') or request.headers.get('X-Visitor-FP')
    if not fingerprint:
        return jsonify({'success': False, 'error': 'Valid fingerprint required'}), 400
    
    existing = SecureRatingManager.check_existing_rating(item_id, fingerprint)
    
    return jsonify({
        'success': True,
        'has_rating': existing['exists'],
        'rating': existing.get('current_rating') if existing['exists'] else None,
        'created_at': existing.get('created_at') if existing['exists'] else None
    })

# Utility function for rate limiting (can be enhanced with Redis)
def check_rate_limit(fingerprint, window_minutes=5, max_requests=10):
    """Simple rate limiting based on fingerprint"""
    with get_db() as conn:
        cursor = conn.cursor()
        
        # Create rate_limit table if it doesn't exist
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS rate_limits (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                fingerprint TEXT NOT NULL,
                endpoint TEXT NOT NULL,
                request_count INTEGER DEFAULT 1,
                window_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX(fingerprint, endpoint)
            )
        ''')
        
        # Clean up old entries
        cutoff_time = datetime.now() - timedelta(minutes=window_minutes)
        cursor.execute('DELETE FROM rate_limits WHERE window_start < ?', (cutoff_time,))
        
        # Check current rate
        cursor.execute('''
            SELECT request_count FROM rate_limits 
            WHERE fingerprint = ? AND endpoint = 'rating' AND window_start >= ?
        ''', (fingerprint, cutoff_time))
        
        result = cursor.fetchone()
        if result and result[0] >= max_requests:
            return False, "Rate limit exceeded"
        
        # Update or insert rate limit record
        if result:
            cursor.execute('''
                UPDATE rate_limits 
                SET request_count = request_count + 1 
                WHERE fingerprint = ? AND endpoint = 'rating' AND window_start >= ?
            ''', (fingerprint, cutoff_time))
        else:
            cursor.execute('''
                INSERT INTO rate_limits (fingerprint, endpoint) 
                VALUES (?, 'rating')
            ''', (fingerprint,))
        
        conn.commit()
        return True, "Rate limit OK"
