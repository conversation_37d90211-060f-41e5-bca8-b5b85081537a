#!/usr/bin/env python3
"""
Database Cleanup Script
Comprehensive database cleanup and optimization tool for the Flask application.

Features:
- Remove unused/redundant tables
- Clean up old/expired data
- Optimize database structure
- Remove orphaned records
- Vacuum and analyze database
- Generate cleanup report
"""

import sqlite3
import os
import sys
import argparse
from datetime import datetime, timedelta
from contextlib import contextmanager

# Database path
DATABASE_PATH = os.path.join(os.path.dirname(__file__), 'app', 'database.db')

@contextmanager
def get_db(db_path):
    """Get database connection with context manager"""
    conn = sqlite3.connect(db_path, detect_types=sqlite3.PARSE_DECLTYPES|sqlite3.PARSE_COLNAMES)
    conn.row_factory = sqlite3.Row
    try:
        yield conn
    finally:
        conn.close()

def get_table_info(cursor):
    """Get information about all tables in the database"""
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
    tables = [row[0] for row in cursor.fetchall()]
    
    table_info = {}
    for table in tables:
        cursor.execute(f"SELECT COUNT(*) FROM {table}")
        count = cursor.fetchone()[0]
        
        cursor.execute(f"PRAGMA table_info({table})")
        columns = cursor.fetchall()
        
        table_info[table] = {
            'count': count,
            'columns': [col[1] for col in columns]
        }
    
    return table_info

def identify_unused_tables(cursor, table_info):
    """Identify tables that appear to be unused or redundant"""
    unused_tables = []
    
    # Tables that might be unused based on analysis
    potentially_unused = [
        'login_attempts',  # Redundant with login_logs
        'session_tokens',  # If using cookie-based auth only
        'app_ratings',     # Redundant with ratings table
        'security_keys',   # If not using encryption features
    ]
    
    for table in potentially_unused:
        if table in table_info:
            if table_info[table]['count'] == 0:
                unused_tables.append(table)
            elif table == 'login_attempts' and 'login_logs' in table_info:
                # login_attempts is redundant if we have login_logs
                unused_tables.append(table)
            elif table == 'app_ratings' and 'ratings' in table_info:
                # app_ratings is old, ratings is the new secure version
                unused_tables.append(table)
    
    return unused_tables

def cleanup_expired_data(cursor, changes):
    """Clean up expired and old data"""
    print("🧹 Cleaning up expired data...")
    
    # 1. Clean up expired nonces (older than 1 hour)
    cursor.execute('DELETE FROM used_nonces WHERE expires_at < datetime("now", "-1 hour")')
    if cursor.rowcount > 0:
        changes.append(f'Removed {cursor.rowcount} expired nonces')
    
    # 2. Clean up old login logs (older than 90 days)
    cursor.execute('DELETE FROM login_logs WHERE timestamp < datetime("now", "-90 days")')
    if cursor.rowcount > 0:
        changes.append(f'Removed {cursor.rowcount} old login log entries')
    
    # 3. Clean up old download logs (older than 180 days)
    cursor.execute('DELETE FROM download_logs WHERE timestamp < datetime("now", "-180 days")')
    if cursor.rowcount > 0:
        changes.append(f'Removed {cursor.rowcount} old download log entries')
    
    # 4. Clean up old admin logs (older than 365 days)
    cursor.execute('DELETE FROM admin_logs WHERE timestamp < datetime("now", "-365 days")')
    if cursor.rowcount > 0:
        changes.append(f'Removed {cursor.rowcount} old admin log entries')
    
    # 5. Clean up expired blocked IPs
    cursor.execute('DELETE FROM blocked_ips WHERE blocked_until < datetime("now") AND is_permanent = 0')
    if cursor.rowcount > 0:
        changes.append(f'Removed {cursor.rowcount} expired IP blocks')
    
    # 6. Clean up old fingerprints (not seen in 365 days)
    cursor.execute('DELETE FROM fingerprints WHERE last_seen < datetime("now", "-365 days")')
    if cursor.rowcount > 0:
        changes.append(f'Removed {cursor.rowcount} old fingerprints')
    
    # 7. Clean up orphaned screenshots (no matching app)
    cursor.execute('''
        DELETE FROM screenshots 
        WHERE app_id NOT IN (SELECT id FROM apps)
    ''')
    if cursor.rowcount > 0:
        changes.append(f'Removed {cursor.rowcount} orphaned screenshots')
    
    # 8. Clean up orphaned shortlinks (no matching user)
    cursor.execute('''
        DELETE FROM shortlinks 
        WHERE user_id NOT IN (SELECT id FROM users)
    ''')
    if cursor.rowcount > 0:
        changes.append(f'Removed {cursor.rowcount} orphaned shortlinks')

def remove_unused_tables(cursor, unused_tables, changes):
    """Remove unused tables"""
    if not unused_tables:
        return
    
    print(f"🗑️  Removing {len(unused_tables)} unused tables...")
    
    for table in unused_tables:
        try:
            cursor.execute(f'DROP TABLE IF EXISTS {table}')
            changes.append(f'Removed unused table: {table}')
        except Exception as e:
            print(f"⚠️  Warning: Could not remove table {table}: {e}")

def optimize_database(cursor, changes):
    """Optimize database structure and performance"""
    print("⚡ Optimizing database...")
    
    # 1. Create missing indexes for better performance
    indexes_to_create = [
        ('idx_apps_category', 'apps', 'category'),
        ('idx_apps_user_id', 'apps', 'user_id'),
        ('idx_apps_created_at', 'apps', 'created_at'),
        ('idx_download_logs_app_id', 'download_logs', 'app_id'),
        ('idx_download_logs_timestamp', 'download_logs', 'timestamp'),
        ('idx_login_logs_timestamp', 'login_logs', 'timestamp'),
        ('idx_login_logs_ip_address', 'login_logs', 'ip_address'),
        ('idx_fingerprints_last_seen', 'fingerprints', 'last_seen'),
        ('idx_blocked_ips_blocked_until', 'blocked_ips', 'blocked_until'),
        ('idx_ratings_item_id', 'ratings', 'item_id'),
        ('idx_referrals_code', 'referrals', 'code'),
    ]
    
    for index_name, table, column in indexes_to_create:
        try:
            cursor.execute(f'CREATE INDEX IF NOT EXISTS {index_name} ON {table}({column})')
            changes.append(f'Created index: {index_name}')
        except Exception as e:
            print(f"⚠️  Warning: Could not create index {index_name}: {e}")
    
    # 2. Update statistics
    cursor.execute('ANALYZE')
    changes.append('Updated database statistics')

def vacuum_database(cursor, changes):
    """Vacuum database to reclaim space"""
    print("🔄 Vacuuming database...")
    try:
        cursor.execute('VACUUM')
        changes.append('Database vacuumed - space reclaimed')
    except Exception as e:
        print(f"⚠️  Warning: Could not vacuum database: {e}")

def generate_report(table_info_before, table_info_after, changes, db_path):
    """Generate cleanup report"""
    print("\n" + "="*60)
    print("📊 DATABASE CLEANUP REPORT")
    print("="*60)
    
    # File size comparison
    if os.path.exists(db_path):
        file_size = os.path.getsize(db_path)
        print(f"Database file: {db_path}")
        print(f"File size: {file_size:,} bytes ({file_size/1024/1024:.2f} MB)")
    
    print(f"\nCleanup completed at: {datetime.now().isoformat()}")
    print(f"Total operations performed: {len(changes)}")
    
    if changes:
        print("\n📋 Changes made:")
        for i, change in enumerate(changes, 1):
            print(f"  {i:2d}. {change}")
    else:
        print("\n✅ No cleanup needed - database is already optimized!")
    
    # Table comparison
    print(f"\n📊 Table summary:")
    print(f"{'Table':<20} {'Before':<10} {'After':<10} {'Change':<10}")
    print("-" * 50)
    
    all_tables = set(table_info_before.keys()) | set(table_info_after.keys())
    for table in sorted(all_tables):
        before = table_info_before.get(table, {}).get('count', 0)
        after = table_info_after.get(table, {}).get('count', 0)
        change = after - before
        change_str = f"{change:+d}" if change != 0 else "0"
        
        if table not in table_info_after:
            print(f"{table:<20} {before:<10} {'REMOVED':<10} {'-':<10}")
        else:
            print(f"{table:<20} {before:<10} {after:<10} {change_str:<10}")
    
    print("="*60)

def main(db_path, dry_run=False, aggressive=False):
    """Main cleanup function"""
    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        sys.exit(1)

    # Backup database before cleanup
    backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    if not dry_run:
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"📦 Database backed up to: {backup_path}")

    changes = []

    with get_db(db_path) as conn:
        cursor = conn.cursor()

        print(f"🔍 Analyzing database: {db_path}")
        table_info_before = get_table_info(cursor)

        print(f"Found {len(table_info_before)} tables with {sum(t['count'] for t in table_info_before.values())} total records")

        if dry_run:
            print("\n🔍 DRY RUN MODE - No changes will be made")
            unused_tables = identify_unused_tables(cursor, table_info_before)
            if unused_tables:
                print(f"Would remove unused tables: {', '.join(unused_tables)}")
            print("Would clean up expired data and optimize database")
            return

        try:
            conn.execute('BEGIN TRANSACTION')

            # 1. Identify and remove unused tables
            unused_tables = identify_unused_tables(cursor, table_info_before)
            if unused_tables:
                print(f"Found {len(unused_tables)} unused tables: {', '.join(unused_tables)}")
                remove_unused_tables(cursor, unused_tables, changes)

            # 2. Clean up expired data (with aggressive mode)
            if aggressive:
                cleanup_expired_data_aggressive(cursor, changes)
            else:
                cleanup_expired_data(cursor, changes)

            # 3. Optimize database
            optimize_database(cursor, changes)

            conn.commit()

            # 4. Vacuum database (must be outside transaction)
            vacuum_database(cursor, changes)

            # 5. Get final table info
            table_info_after = get_table_info(cursor)

            # 6. Generate report
            generate_report(table_info_before, table_info_after, changes, db_path)

        except Exception as e:
            conn.rollback()
            print(f"\n❌ ERROR: Cleanup failed: {e}")
            print("All changes have been rolled back.")
            # Remove backup if cleanup failed
            if os.path.exists(backup_path):
                os.remove(backup_path)
            sys.exit(1)

def cleanup_expired_data_aggressive(cursor, changes):
    """More aggressive cleanup with shorter retention periods"""
    print("🧹 Aggressive cleanup mode - shorter retention periods...")

    # 1. Clean up expired nonces (older than 30 minutes)
    cursor.execute('DELETE FROM used_nonces WHERE expires_at < datetime("now", "-30 minutes")')
    if cursor.rowcount > 0:
        changes.append(f'Removed {cursor.rowcount} expired nonces (aggressive)')

    # 2. Clean up old login logs (older than 30 days)
    cursor.execute('DELETE FROM login_logs WHERE timestamp < datetime("now", "-30 days")')
    if cursor.rowcount > 0:
        changes.append(f'Removed {cursor.rowcount} old login log entries (aggressive)')

    # 3. Clean up old download logs (older than 60 days)
    cursor.execute('DELETE FROM download_logs WHERE timestamp < datetime("now", "-60 days")')
    if cursor.rowcount > 0:
        changes.append(f'Removed {cursor.rowcount} old download log entries (aggressive)')

    # 4. Clean up old admin logs (older than 180 days)
    cursor.execute('DELETE FROM admin_logs WHERE timestamp < datetime("now", "-180 days")')
    if cursor.rowcount > 0:
        changes.append(f'Removed {cursor.rowcount} old admin log entries (aggressive)')

    # 5. Clean up old fingerprints (not seen in 180 days)
    cursor.execute('DELETE FROM fingerprints WHERE last_seen < datetime("now", "-180 days")')
    if cursor.rowcount > 0:
        changes.append(f'Removed {cursor.rowcount} old fingerprints (aggressive)')

    # 6. Clean up expired blocked IPs
    cursor.execute('DELETE FROM blocked_ips WHERE blocked_until < datetime("now") AND is_permanent = 0')
    if cursor.rowcount > 0:
        changes.append(f'Removed {cursor.rowcount} expired IP blocks')

    # 7. Clean up orphaned data
    cursor.execute('DELETE FROM screenshots WHERE app_id NOT IN (SELECT id FROM apps)')
    if cursor.rowcount > 0:
        changes.append(f'Removed {cursor.rowcount} orphaned screenshots')

    cursor.execute('DELETE FROM shortlinks WHERE user_id NOT IN (SELECT id FROM users)')
    if cursor.rowcount > 0:
        changes.append(f'Removed {cursor.rowcount} orphaned shortlinks')

if __name__ == '__main__':
    parser = argparse.ArgumentParser(
        description='Database Cleanup and Optimization Tool',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog='''
Examples:
  python cleanup.py                          # Clean default database
  python cleanup.py --database custom.db    # Clean custom database
  python cleanup.py --dry-run               # Show what would be cleaned
  python cleanup.py --aggressive            # More aggressive cleanup

This script performs comprehensive database cleanup:
- Removes unused/redundant tables
- Cleans up expired and old data
- Optimizes database structure and indexes
- Removes orphaned records
- Vacuums database to reclaim space
- Creates automatic backup before cleanup

The script is safe and creates backups automatically.
        '''
    )

    parser.add_argument(
        '--database',
        default=DATABASE_PATH,
        help='Path to database file (default: app/database.db)'
    )

    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Show what would be cleaned without making changes'
    )

    parser.add_argument(
        '--aggressive',
        action='store_true',
        help='More aggressive cleanup (shorter retention periods)'
    )

    args = parser.parse_args()

    db_path = os.path.abspath(args.database)

    print("🧹 DATABASE CLEANUP TOOL")
    print("="*50)
    print(f"Database: {db_path}")
    print(f"Mode: {'DRY RUN' if args.dry_run else 'CLEANUP'}")
    if args.aggressive:
        print("Aggressive mode: ON")
    print("="*50)

    main(db_path, args.dry_run, args.aggressive)
