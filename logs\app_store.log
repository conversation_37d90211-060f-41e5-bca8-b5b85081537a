2025-07-09 18:32:47,883 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-07-09 18:32:47,883 INFO: [33mPress CTRL+C to quit[0m
2025-07-09 18:32:51,916 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:51] "GET / HTTP/1.1" 200 -
2025-07-09 18:32:52,877 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:52] "GET /static/js/app.js HTTP/1.1" 200 -
2025-07-09 18:32:52,883 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:52] "GET /static/css/style.css HTTP/1.1" 200 -
2025-07-09 18:32:52,885 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:52] "GET /uploads/icons/20250613_015832_2.webp HTTP/1.1" 200 -
2025-07-09 18:32:52,887 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:52] "GET /uploads/icons/20250617_202240_2.webp HTTP/1.1" 200 -
2025-07-09 18:32:52,914 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:52] "GET /uploads/icons/20250613_001329_HLk9Vn2d.webp HTTP/1.1" 200 -
2025-07-09 18:32:52,933 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:52] "GET /uploads/icons/20250612_235643_q8gEGVAp.webp HTTP/1.1" 200 -
2025-07-09 18:32:52,934 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:52] "GET /uploads/icons/20250613_000452_Ou2ESueq.webp HTTP/1.1" 200 -
2025-07-09 18:32:53,438 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:53] "GET /uploads/icons/20250612_151120_1.webp HTTP/1.1" 200 -
2025-07-09 18:32:53,449 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:53] "GET /uploads/icons/20250611_190659_venom.webp HTTP/1.1" 200 -
2025-07-09 18:32:53,450 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:53] "GET /uploads/icons/20250611_191431_2.webp HTTP/1.1" 200 -
2025-07-09 18:32:53,462 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:53] "GET /uploads/icons/20250611_221720_1.webp HTTP/1.1" 200 -
2025-07-09 18:32:53,464 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:53] "GET /uploads/icons/20250611_191905_dnspy.webp HTTP/1.1" 200 -
2025-07-09 18:32:54,485 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:54] "GET /static/favicon.ico HTTP/1.1" 200 -
2025-07-09 18:32:54,802 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:54] "[32mGET /rce HTTP/1.1[0m" 302 -
2025-07-09 18:32:54,816 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:54] "GET /gate?next=/ HTTP/1.1" 200 -
2025-07-09 18:32:54,912 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:54] "GET /static/js/client.js HTTP/1.1" 200 -
2025-07-09 18:32:54,934 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:54] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:32:54,936 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:32:54,999 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:54] "GET /fp/nonce HTTP/1.1" 200 -
2025-07-09 18:32:55,865 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:55] "GET / HTTP/1.1" 200 -
2025-07-09 18:32:55,884 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:55] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:32:55,886 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:55] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:32:55,890 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:55] "[36mGET /uploads/icons/20250617_202240_2.webp HTTP/1.1[0m" 304 -
2025-07-09 18:32:55,893 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:55] "[36mGET /uploads/icons/20250613_015832_2.webp HTTP/1.1[0m" 304 -
2025-07-09 18:32:55,910 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:55] "[36mGET /uploads/icons/20250613_001329_HLk9Vn2d.webp HTTP/1.1[0m" 304 -
2025-07-09 18:32:55,917 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:55] "[36mGET /uploads/icons/20250613_000452_Ou2ESueq.webp HTTP/1.1[0m" 304 -
2025-07-09 18:32:55,932 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:55] "[36mGET /uploads/icons/20250612_235643_q8gEGVAp.webp HTTP/1.1[0m" 304 -
2025-07-09 18:32:55,933 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:55] "[36mGET /uploads/icons/20250612_151120_1.webp HTTP/1.1[0m" 304 -
2025-07-09 18:32:55,947 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:55] "[36mGET /uploads/icons/20250611_221720_1.webp HTTP/1.1[0m" 304 -
2025-07-09 18:32:55,948 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:55] "[36mGET /uploads/icons/20250611_191431_2.webp HTTP/1.1[0m" 304 -
2025-07-09 18:32:55,949 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:55] "[36mGET /uploads/icons/20250611_190659_venom.webp HTTP/1.1[0m" 304 -
2025-07-09 18:32:55,959 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:55] "[36mGET /uploads/icons/20250611_191905_dnspy.webp HTTP/1.1[0m" 304 -
2025-07-09 18:32:59,895 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:33:03,096 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:03] "[32mGET /rce HTTP/1.1[0m" 302 -
2025-07-09 18:33:03,105 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:03] "GET /gate?next=/ HTTP/1.1" 200 -
2025-07-09 18:33:03,134 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:03] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:33:03,135 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:03] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:33:03,143 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:03] "[36mGET /static/js/client.js HTTP/1.1[0m" 304 -
2025-07-09 18:33:03,206 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:03] "GET /fp/nonce HTTP/1.1" 200 -
2025-07-09 18:33:04,157 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:04] "POST /fp/collect HTTP/1.1" 200 -
2025-07-09 18:33:04,697 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:04] "GET / HTTP/1.1" 200 -
2025-07-09 18:33:04,722 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:04] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:33:04,729 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:04] "[36mGET /uploads/icons/20250617_202240_2.webp HTTP/1.1[0m" 304 -
2025-07-09 18:33:04,733 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:04] "[36mGET /uploads/icons/20250613_015832_2.webp HTTP/1.1[0m" 304 -
2025-07-09 18:33:04,737 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:04] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:33:04,765 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:04] "[36mGET /uploads/icons/20250613_001329_HLk9Vn2d.webp HTTP/1.1[0m" 304 -
2025-07-09 18:33:04,772 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:04] "[36mGET /uploads/icons/20250613_000452_Ou2ESueq.webp HTTP/1.1[0m" 304 -
2025-07-09 18:33:04,782 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:04] "[36mGET /uploads/icons/20250612_235643_q8gEGVAp.webp HTTP/1.1[0m" 304 -
2025-07-09 18:33:04,784 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:04] "[36mGET /uploads/icons/20250612_151120_1.webp HTTP/1.1[0m" 304 -
2025-07-09 18:33:04,789 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:04] "[36mGET /uploads/icons/20250611_191431_2.webp HTTP/1.1[0m" 304 -
2025-07-09 18:33:04,794 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:04] "[36mGET /uploads/icons/20250611_190659_venom.webp HTTP/1.1[0m" 304 -
2025-07-09 18:33:04,797 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:04] "[36mGET /uploads/icons/20250611_221720_1.webp HTTP/1.1[0m" 304 -
2025-07-09 18:33:04,815 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:04] "[36mGET /uploads/icons/20250611_191905_dnspy.webp HTTP/1.1[0m" 304 -
2025-07-09 18:33:06,293 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:06] "GET /rce HTTP/1.1" 200 -
2025-07-09 18:33:06,317 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:06] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:33:06,319 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:33:48,648 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:48] "GET /rce HTTP/1.1" 200 -
2025-07-09 18:33:48,674 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:48] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:33:48,675 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:33:48,856 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:48] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-09 18:33:52,902 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-07-09 18:33:52,902 INFO: [33mPress CTRL+C to quit[0m
2025-07-09 18:33:52,906 INFO:  * Restarting with stat
2025-07-09 18:33:53,853 WARNING:  * Debugger is active!
2025-07-09 18:33:53,860 INFO:  * Debugger PIN: 437-432-840
2025-07-09 18:33:54,008 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:54] "GET /rce HTTP/1.1" 200 -
2025-07-09 18:33:54,095 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:54] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:33:54,097 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:33:54,164 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:54] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-09 18:50:47,724 INFO: 127.0.0.1 - - [09/Jul/2025 18:50:47] "GET /rce HTTP/1.1" 200 -
2025-07-09 18:50:48,108 INFO: 127.0.0.1 - - [09/Jul/2025 18:50:48] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:50:48,127 INFO: 127.0.0.1 - - [09/Jul/2025 18:50:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:50:48,468 INFO: 127.0.0.1 - - [09/Jul/2025 18:50:48] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-09 18:51:58,533 INFO: 127.0.0.1 - - [09/Jul/2025 18:51:58] "GET /rce HTTP/1.1" 200 -
2025-07-09 18:51:58,564 INFO: 127.0.0.1 - - [09/Jul/2025 18:51:58] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:51:58,568 INFO: 127.0.0.1 - - [09/Jul/2025 18:51:58] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:51:58,682 INFO: 127.0.0.1 - - [09/Jul/2025 18:51:58] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-09 18:53:02,160 INFO: 127.0.0.1 - - [09/Jul/2025 18:53:02] "GET /rce HTTP/1.1" 200 -
2025-07-09 18:53:02,204 INFO: 127.0.0.1 - - [09/Jul/2025 18:53:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:53:02,206 INFO: 127.0.0.1 - - [09/Jul/2025 18:53:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:53:02,322 INFO: 127.0.0.1 - - [09/Jul/2025 18:53:02] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-09 18:53:07,294 INFO: 127.0.0.1 - - [09/Jul/2025 18:53:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:53:09,132 INFO: 127.0.0.1 - - [09/Jul/2025 18:53:09] "GET /rce HTTP/1.1" 200 -
2025-07-09 18:53:09,168 INFO: 127.0.0.1 - - [09/Jul/2025 18:53:09] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:53:09,173 INFO: 127.0.0.1 - - [09/Jul/2025 18:53:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:53:09,514 INFO: 127.0.0.1 - - [09/Jul/2025 18:53:09] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-09 18:53:58,841 INFO: 127.0.0.1 - - [09/Jul/2025 18:53:58] "GET /rce HTTP/1.1" 200 -
2025-07-09 18:53:58,883 INFO: 127.0.0.1 - - [09/Jul/2025 18:53:58] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:53:58,886 INFO: 127.0.0.1 - - [09/Jul/2025 18:53:58] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:53:59,299 INFO: 127.0.0.1 - - [09/Jul/2025 18:53:59] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-09 18:54:34,510 INFO: 127.0.0.1 - - [09/Jul/2025 18:54:34] "GET /rce HTTP/1.1" 200 -
2025-07-09 18:54:34,575 INFO: 127.0.0.1 - - [09/Jul/2025 18:54:34] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:54:34,579 INFO: 127.0.0.1 - - [09/Jul/2025 18:54:34] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:54:34,973 INFO: 127.0.0.1 - - [09/Jul/2025 18:54:34] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-09 18:54:38,372 INFO: 127.0.0.1 - - [09/Jul/2025 18:54:38] "GET /rce HTTP/1.1" 200 -
2025-07-09 18:54:38,431 INFO: 127.0.0.1 - - [09/Jul/2025 18:54:38] "GET /static/js/app.js HTTP/1.1" 200 -
2025-07-09 18:54:38,434 INFO: 127.0.0.1 - - [09/Jul/2025 18:54:38] "GET /static/css/style.css HTTP/1.1" 200 -
2025-07-09 18:54:43,607 INFO: 127.0.0.1 - - [09/Jul/2025 18:54:43] "GET /static/favicon.ico HTTP/1.1" 200 -
2025-07-09 18:55:02,104 INFO: 127.0.0.1 - - [09/Jul/2025 18:55:02] "GET /rce HTTP/1.1" 200 -
2025-07-09 18:55:02,150 INFO: 127.0.0.1 - - [09/Jul/2025 18:55:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:55:02,156 INFO: 127.0.0.1 - - [09/Jul/2025 18:55:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:55:02,532 INFO: 127.0.0.1 - - [09/Jul/2025 18:55:02] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-09 18:55:36,113 INFO: 127.0.0.1 - - [09/Jul/2025 18:55:36] "GET /rce HTTP/1.1" 200 -
2025-07-09 18:55:36,174 INFO: 127.0.0.1 - - [09/Jul/2025 18:55:36] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:55:36,177 INFO: 127.0.0.1 - - [09/Jul/2025 18:55:36] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:55:36,401 INFO: 127.0.0.1 - - [09/Jul/2025 18:55:36] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-09 18:56:32,350 INFO: 127.0.0.1 - - [09/Jul/2025 18:56:32] "GET /rce HTTP/1.1" 200 -
2025-07-09 18:56:32,418 INFO: 127.0.0.1 - - [09/Jul/2025 18:56:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:56:32,438 INFO: 127.0.0.1 - - [09/Jul/2025 18:56:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:56:32,721 INFO: 127.0.0.1 - - [09/Jul/2025 18:56:32] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-09 19:01:21,895 INFO: 127.0.0.1 - - [09/Jul/2025 19:01:21] "GET /rce HTTP/1.1" 200 -
2025-07-09 19:01:21,946 INFO: 127.0.0.1 - - [09/Jul/2025 19:01:21] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 19:01:21,951 INFO: 127.0.0.1 - - [09/Jul/2025 19:01:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 19:01:22,061 INFO: 127.0.0.1 - - [09/Jul/2025 19:01:22] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-09 19:17:17,470 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\main.py', reloading
2025-07-09 19:17:17,577 INFO:  * Restarting with stat
2025-07-09 19:17:19,212 WARNING:  * Debugger is active!
2025-07-09 19:17:19,228 INFO:  * Debugger PIN: 437-432-840
2025-07-09 19:48:50,610 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-07-09 19:48:50,612 INFO: [33mPress CTRL+C to quit[0m
2025-07-09 19:48:57,288 ERROR: Exception on / [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\blueprints\main.py", line 89, in index
    return render_template('index.html',
                         apps=apps,
    ...<6 lines>...
                         has_next=has_next,
                         metadata=metadata)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\index.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\base.html", line 80, in top-level template code
    <!-- <a class="pepe-nav-link" href="{{ url_for('main.rce') }}">RCE</a> -->
    ^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 1121, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 1110, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
        endpoint,
    ...<3 lines>...
        force_external=_external,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\werkzeug\routing\map.py", line 924, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'main.rce'. Did you mean 'main.gate' instead?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\blueprints\main.py", line 103, in index
    return render_template('index.html',
                         apps=type('AppList', (), {'items': [], 'total': 0, 'pages': 1, 'page': 1, 'has_prev': False, 'has_next': False, 'iter_pages': lambda: [1]})(),
    ...<6 lines>...
                         has_next=False,
                         metadata=MetadataGenerator.get_default_metadata())
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\index.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\base.html", line 80, in top-level template code
    <!-- <a class="pepe-nav-link" href="{{ url_for('main.rce') }}">RCE</a> -->
    ^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 1121, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 1110, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
        endpoint,
    ...<3 lines>...
        force_external=_external,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\werkzeug\routing\map.py", line 924, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'main.rce'. Did you mean 'main.gate' instead?
2025-07-09 19:48:57,327 INFO: 127.0.0.1 - - [09/Jul/2025 19:48:57] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-07-09 19:48:57,387 INFO: 127.0.0.1 - - [09/Jul/2025 19:48:57] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-09 19:49:08,562 ERROR: Exception on / [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\blueprints\main.py", line 89, in index
    return render_template('index.html',
                         apps=apps,
    ...<6 lines>...
                         has_next=has_next,
                         metadata=metadata)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\index.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\base.html", line 80, in top-level template code
    <!-- <a class="pepe-nav-link" href="{{ url_for('main.rce') }}">RCE</a> -->
    ^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 1121, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 1110, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
        endpoint,
    ...<3 lines>...
        force_external=_external,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\werkzeug\routing\map.py", line 924, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'main.rce'. Did you mean 'main.gate' instead?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\blueprints\main.py", line 103, in index
    return render_template('index.html',
                         apps=type('AppList', (), {'items': [], 'total': 0, 'pages': 1, 'page': 1, 'has_prev': False, 'has_next': False, 'iter_pages': lambda: [1]})(),
    ...<6 lines>...
                         has_next=False,
                         metadata=MetadataGenerator.get_default_metadata())
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\index.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\base.html", line 80, in top-level template code
    <!-- <a class="pepe-nav-link" href="{{ url_for('main.rce') }}">RCE</a> -->
    ^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 1121, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 1110, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
        endpoint,
    ...<3 lines>...
        force_external=_external,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\werkzeug\routing\map.py", line 924, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'main.rce'. Did you mean 'main.gate' instead?
2025-07-09 19:49:08,602 INFO: 127.0.0.1 - - [09/Jul/2025 19:49:08] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-07-09 19:55:12,990 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-07-09 19:55:12,991 INFO: [33mPress CTRL+C to quit[0m
2025-07-09 19:55:15,757 ERROR: Exception on / [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\blueprints\main.py", line 89, in index
    return render_template('index.html',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Program Files\Python311\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\index.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\base.html", line 80, in top-level template code
    <!-- <a class="pepe-nav-link" href="{{ url_for('main.rce') }}">RCE</a> -->
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1121, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1110, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\werkzeug\routing\map.py", line 924, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'main.rce'. Did you mean 'main.gate' instead?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\blueprints\main.py", line 103, in index
    return render_template('index.html',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Program Files\Python311\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\index.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\base.html", line 80, in top-level template code
    <!-- <a class="pepe-nav-link" href="{{ url_for('main.rce') }}">RCE</a> -->
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1121, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1110, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\werkzeug\routing\map.py", line 924, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'main.rce'. Did you mean 'main.gate' instead?
2025-07-09 19:55:15,774 INFO: 127.0.0.1 - - [09/Jul/2025 19:55:15] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-07-09 19:55:16,515 ERROR: Exception on / [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\blueprints\main.py", line 89, in index
    return render_template('index.html',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Program Files\Python311\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\index.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\base.html", line 80, in top-level template code
    <!-- <a class="pepe-nav-link" href="{{ url_for('main.rce') }}">RCE</a> -->
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1121, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1110, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\werkzeug\routing\map.py", line 924, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'main.rce'. Did you mean 'main.gate' instead?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\blueprints\main.py", line 103, in index
    return render_template('index.html',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Program Files\Python311\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\index.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\base.html", line 80, in top-level template code
    <!-- <a class="pepe-nav-link" href="{{ url_for('main.rce') }}">RCE</a> -->
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1121, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1110, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\werkzeug\routing\map.py", line 924, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'main.rce'. Did you mean 'main.gate' instead?
2025-07-09 19:55:16,524 INFO: 127.0.0.1 - - [09/Jul/2025 19:55:16] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-07-09 19:55:55,905 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-07-09 19:55:55,906 INFO: [33mPress CTRL+C to quit[0m
2025-07-09 19:55:55,911 INFO:  * Restarting with stat
2025-07-09 19:55:57,219 WARNING:  * Debugger is active!
2025-07-09 19:55:57,227 INFO:  * Debugger PIN: 437-432-840
2025-07-09 19:55:57,476 INFO: 127.0.0.1 - - [09/Jul/2025 19:55:57] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-07-09 19:55:57,580 INFO: 127.0.0.1 - - [09/Jul/2025 19:55:57] "GET /?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-07-09 19:55:57,581 INFO: 127.0.0.1 - - [09/Jul/2025 19:55:57] "GET /?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-07-09 19:55:57,682 INFO: 127.0.0.1 - - [09/Jul/2025 19:55:57] "GET /?__debugger__=yes&cmd=resource&f=console.png&s=dFHiD1LLOU5uBZz3PCBH HTTP/1.1" 200 -
2025-07-09 19:55:57,703 INFO: 127.0.0.1 - - [09/Jul/2025 19:55:57] "GET /?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-07-09 19:56:25,288 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:25] "GET / HTTP/1.1" 200 -
2025-07-09 19:56:25,323 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:25] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 19:56:25,331 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:25] "[36mGET /uploads/icons/20250613_015832_2.webp HTTP/1.1[0m" 304 -
2025-07-09 19:56:25,336 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:25] "[36mGET /uploads/icons/20250617_202240_2.webp HTTP/1.1[0m" 304 -
2025-07-09 19:56:25,350 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 19:56:25,354 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:25] "[36mGET /uploads/icons/20250613_000452_Ou2ESueq.webp HTTP/1.1[0m" 304 -
2025-07-09 19:56:25,365 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:25] "[36mGET /uploads/icons/20250613_001329_HLk9Vn2d.webp HTTP/1.1[0m" 304 -
2025-07-09 19:56:25,395 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:25] "[36mGET /uploads/icons/20250611_221720_1.webp HTTP/1.1[0m" 304 -
2025-07-09 19:56:25,396 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:25] "[36mGET /uploads/icons/20250611_191431_2.webp HTTP/1.1[0m" 304 -
2025-07-09 19:56:25,406 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:25] "[36mGET /uploads/icons/20250612_151120_1.webp HTTP/1.1[0m" 304 -
2025-07-09 19:56:25,421 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:25] "[36mGET /uploads/icons/20250612_235643_q8gEGVAp.webp HTTP/1.1[0m" 304 -
2025-07-09 19:56:25,431 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:25] "[36mGET /uploads/icons/20250611_191905_dnspy.webp HTTP/1.1[0m" 304 -
2025-07-09 19:56:25,442 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:25] "[36mGET /uploads/icons/20250611_190659_venom.webp HTTP/1.1[0m" 304 -
2025-07-09 19:56:26,269 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:26] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-09 19:56:34,011 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:34] "GET /app/14 HTTP/1.1" 200 -
2025-07-09 19:56:34,052 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:34] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 19:56:34,053 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:34] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 19:56:34,072 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:34] "GET /uploads/screenshots/20250613_015832_Screenshot_1.webp HTTP/1.1" 200 -
2025-07-09 19:56:34,082 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:34] "[36mGET /uploads/icons/20250613_015832_2.webp HTTP/1.1[0m" 304 -
2025-07-09 19:56:34,238 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:34] "GET /fp/nonce HTTP/1.1" 200 -
2025-07-09 19:56:36,396 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:36] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 19:56:46,126 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:46] "[32mGET /app/14 HTTP/1.1[0m" 302 -
2025-07-09 19:56:46,145 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:46] "GET /gate?next=/app/14 HTTP/1.1" 200 -
2025-07-09 19:56:46,435 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:46] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 19:56:46,439 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:46] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 19:56:46,444 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:46] "[36mGET /static/js/client.js HTTP/1.1[0m" 304 -
2025-07-09 19:56:46,470 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:46] "GET /fp/nonce HTTP/1.1" 200 -
2025-07-09 19:56:47,453 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:47] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-09 19:56:47,818 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:47] "POST /fp/collect HTTP/1.1" 200 -
2025-07-09 19:56:48,372 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:48] "GET /app/14 HTTP/1.1" 200 -
2025-07-09 19:56:48,409 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 19:56:48,417 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:48] "[36mGET /uploads/icons/20250613_015832_2.webp HTTP/1.1[0m" 304 -
2025-07-09 19:56:48,420 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:48] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 19:56:48,422 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:48] "[36mGET /uploads/screenshots/20250613_015832_Screenshot_1.webp HTTP/1.1[0m" 304 -
2025-07-09 19:56:48,628 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:48] "GET /fp/nonce HTTP/1.1" 200 -
2025-07-09 20:02:41,606 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\security.py', reloading
2025-07-09 20:02:41,709 INFO:  * Restarting with stat
2025-07-09 20:02:43,416 WARNING:  * Debugger is active!
2025-07-09 20:02:43,427 INFO:  * Debugger PIN: 437-432-840
2025-07-09 20:16:16,327 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 20:16:16,394 INFO:  * Restarting with stat
2025-07-09 20:16:17,512 WARNING:  * Debugger is active!
2025-07-09 20:16:17,540 INFO:  * Debugger PIN: 437-432-840
2025-07-09 20:16:29,741 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 20:16:29,824 INFO:  * Restarting with stat
2025-07-09 20:16:30,731 WARNING:  * Debugger is active!
2025-07-09 20:16:30,738 INFO:  * Debugger PIN: 437-432-840
2025-07-09 20:17:24,201 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-07-09 20:17:24,202 INFO: [33mPress CTRL+C to quit[0m
2025-07-09 20:18:41,621 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:41] "GET / HTTP/1.1" 200 -
2025-07-09 20:18:42,668 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:42] "GET / HTTP/1.1" 200 -
2025-07-09 20:18:43,698 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:43] "GET / HTTP/1.1" 200 -
2025-07-09 20:18:44,749 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:44] "GET / HTTP/1.1" 200 -
2025-07-09 20:18:46,224 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:46] "GET / HTTP/1.1" 200 -
2025-07-09 20:18:47,280 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:47] "GET / HTTP/1.1" 200 -
2025-07-09 20:18:48,305 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:48] "[32mGET /sitemap.xml HTTP/1.1[0m" 302 -
2025-07-09 20:18:49,327 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:49] "GET /gate?next=/ HTTP/1.1" 200 -
2025-07-09 20:18:50,340 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:50] "[32mGET /sitemap.xml HTTP/1.1[0m" 302 -
2025-07-09 20:18:51,359 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:51] "GET /gate?next=/ HTTP/1.1" 200 -
2025-07-09 20:18:52,423 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:52] "[32mGET /sitemap.xml HTTP/1.1[0m" 302 -
2025-07-09 20:18:53,438 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:53] "GET /gate?next=/ HTTP/1.1" 200 -
2025-07-09 20:18:54,469 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:54] "[32mGET /sitemap.xml HTTP/1.1[0m" 302 -
2025-07-09 20:18:55,489 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:55] "GET /gate?next=/ HTTP/1.1" 200 -
2025-07-09 20:18:56,503 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:56] "[32mGET /sitemap.xml HTTP/1.1[0m" 302 -
2025-07-09 20:18:57,522 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:57] "GET /gate?next=/ HTTP/1.1" 200 -
2025-07-09 20:18:58,538 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:58] "[32mGET /sitemap.xml HTTP/1.1[0m" 302 -
2025-07-09 20:18:59,555 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:59] "GET /gate?next=/ HTTP/1.1" 200 -
2025-07-09 20:19:00,572 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:00] "[32mGET /robots.txt HTTP/1.1[0m" 302 -
2025-07-09 20:19:01,588 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:01] "GET /gate?next=/ HTTP/1.1" 200 -
2025-07-09 20:19:02,606 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:02] "[32mGET /robots.txt HTTP/1.1[0m" 302 -
2025-07-09 20:19:03,636 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:03] "GET /gate?next=/ HTTP/1.1" 200 -
2025-07-09 20:19:04,654 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:04] "[32mGET /robots.txt HTTP/1.1[0m" 302 -
2025-07-09 20:19:05,677 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:05] "GET /gate?next=/ HTTP/1.1" 200 -
2025-07-09 20:19:06,701 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:06] "[32mGET /robots.txt HTTP/1.1[0m" 302 -
2025-07-09 20:19:07,722 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:07] "GET /gate?next=/ HTTP/1.1" 200 -
2025-07-09 20:19:08,741 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:08] "[32mGET /robots.txt HTTP/1.1[0m" 302 -
2025-07-09 20:19:09,761 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:09] "GET /gate?next=/ HTTP/1.1" 200 -
2025-07-09 20:19:10,788 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:10] "[32mGET /robots.txt HTTP/1.1[0m" 302 -
2025-07-09 20:19:11,821 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:11] "GET /gate?next=/ HTTP/1.1" 200 -
2025-07-09 20:19:12,852 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:12] "GET / HTTP/1.1" 200 -
2025-07-09 20:19:13,901 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:13] "GET / HTTP/1.1" 200 -
2025-07-09 20:19:14,943 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:14] "GET / HTTP/1.1" 200 -
2025-07-09 20:19:15,985 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:15] "GET / HTTP/1.1" 200 -
2025-07-09 20:19:17,035 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:17] "GET / HTTP/1.1" 200 -
2025-07-09 20:19:18,063 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:18] "GET / HTTP/1.1" 200 -
2025-07-09 20:59:49,308 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-07-09 20:59:49,308 INFO: [33mPress CTRL+C to quit[0m
2025-07-09 21:00:01,403 INFO: 127.0.0.1 - - [09/Jul/2025 21:00:01] "[32mGET /app/7 HTTP/1.1[0m" 302 -
2025-07-09 21:00:14,816 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:00:14,880 INFO:  * Restarting with stat
2025-07-09 21:00:15,680 WARNING:  * Debugger is active!
2025-07-09 21:00:15,705 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:00:18,875 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:00:21,573 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-07-09 21:00:21,574 INFO: [33mPress CTRL+C to quit[0m
2025-07-09 21:00:21,574 INFO:  * Restarting with stat
2025-07-09 21:00:22,300 WARNING:  * Debugger is active!
2025-07-09 21:00:22,356 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:01:00,304 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-07-09 21:01:00,305 INFO: [33mPress CTRL+C to quit[0m
2025-07-09 21:01:00,306 INFO:  * Restarting with stat
2025-07-09 21:01:00,999 WARNING:  * Debugger is active!
2025-07-09 21:01:01,003 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:01:43,719 INFO: 127.0.0.1 - - [09/Jul/2025 21:01:43] "[32mGET /app/7 HTTP/1.1[0m" 302 -
2025-07-09 21:02:25,694 INFO: 127.0.0.1 - - [09/Jul/2025 21:02:25] "[32mGET /app/7 HTTP/1.1[0m" 302 -
2025-07-09 21:02:58,454 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:02:58,524 INFO:  * Restarting with stat
2025-07-09 21:02:59,185 WARNING:  * Debugger is active!
2025-07-09 21:02:59,189 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:03:04,271 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:03:04,342 INFO:  * Restarting with stat
2025-07-09 21:03:04,976 WARNING:  * Debugger is active!
2025-07-09 21:03:04,980 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:03:07,030 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:03:07,113 INFO:  * Restarting with stat
2025-07-09 21:03:07,872 WARNING:  * Debugger is active!
2025-07-09 21:03:07,876 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:03:30,146 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:03:30,223 INFO:  * Restarting with stat
2025-07-09 21:03:31,065 WARNING:  * Debugger is active!
2025-07-09 21:03:31,074 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:03:41,211 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:03:41,295 INFO:  * Restarting with stat
2025-07-09 21:03:42,039 WARNING:  * Debugger is active!
2025-07-09 21:03:42,043 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:08:02,967 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\main.py', reloading
2025-07-09 21:08:03,038 INFO:  * Restarting with stat
2025-07-09 21:08:03,671 WARNING:  * Debugger is active!
2025-07-09 21:08:03,678 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:08:15,847 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\auth.py', reloading
2025-07-09 21:08:15,994 INFO:  * Restarting with stat
2025-07-09 21:08:16,581 WARNING:  * Debugger is active!
2025-07-09 21:08:16,585 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:08:52,119 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-07-09 21:08:52,121 INFO: [33mPress CTRL+C to quit[0m
2025-07-09 21:09:40,959 INFO: 127.0.0.1 - - [09/Jul/2025 21:09:40] "GET / HTTP/1.1" 200 -
2025-07-09 21:09:41,993 INFO: 127.0.0.1 - - [09/Jul/2025 21:09:41] "GET / HTTP/1.1" 200 -
2025-07-09 21:09:43,027 INFO: 127.0.0.1 - - [09/Jul/2025 21:09:43] "GET / HTTP/1.1" 200 -
2025-07-09 21:09:44,057 INFO: 127.0.0.1 - - [09/Jul/2025 21:09:44] "GET /sitemap.xml HTTP/1.1" 200 -
2025-07-09 21:09:45,098 INFO: 127.0.0.1 - - [09/Jul/2025 21:09:45] "GET /sitemap.xml HTTP/1.1" 200 -
2025-07-09 21:13:47,387 INFO: 127.0.0.1 - - [09/Jul/2025 21:13:47] "GET /app/7 HTTP/1.1" 200 -
2025-07-09 21:13:50,166 INFO: 127.0.0.1 - - [09/Jul/2025 21:13:50] "GET /app/7 HTTP/1.1" 200 -
2025-07-09 21:13:55,687 INFO: 127.0.0.1 - - [09/Jul/2025 21:13:55] "[32mGET /app/7 HTTP/1.1[0m" 302 -
2025-07-09 21:15:09,086 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\auth.py', reloading
2025-07-09 21:15:09,160 INFO:  * Restarting with stat
2025-07-09 21:15:10,352 WARNING:  * Debugger is active!
2025-07-09 21:15:10,359 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:17:01,656 INFO: 127.0.0.1 - - [09/Jul/2025 21:17:01] "[32mGET /app/7 HTTP/1.1[0m" 302 -
2025-07-09 21:21:01,007 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:21:01,073 INFO:  * Restarting with stat
2025-07-09 21:21:01,982 WARNING:  * Debugger is active!
2025-07-09 21:21:01,985 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:21:31,328 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:21:31,389 INFO:  * Restarting with stat
2025-07-09 21:21:32,245 WARNING:  * Debugger is active!
2025-07-09 21:21:32,248 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:21:58,637 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:21:58,702 INFO:  * Restarting with stat
2025-07-09 21:21:59,568 WARNING:  * Debugger is active!
2025-07-09 21:21:59,578 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:22:58,997 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:22:59,103 INFO:  * Restarting with stat
2025-07-09 21:23:00,434 WARNING:  * Debugger is active!
2025-07-09 21:23:00,439 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:26:46,729 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:26:46,804 INFO:  * Restarting with stat
2025-07-09 21:26:47,423 WARNING:  * Debugger is active!
2025-07-09 21:26:47,427 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:26:50,491 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:26:50,552 INFO:  * Restarting with stat
2025-07-09 21:26:51,271 WARNING:  * Debugger is active!
2025-07-09 21:26:51,271 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:26:55,345 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:26:55,401 INFO:  * Restarting with stat
2025-07-09 21:26:56,015 WARNING:  * Debugger is active!
2025-07-09 21:26:56,041 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:26:58,134 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:26:58,241 INFO:  * Restarting with stat
2025-07-09 21:26:58,920 WARNING:  * Debugger is active!
2025-07-09 21:26:58,924 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:29:12,270 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:29:12,402 INFO:  * Restarting with stat
2025-07-09 21:29:13,975 WARNING:  * Debugger is active!
2025-07-09 21:29:13,980 INFO:  * Debugger PIN: 437-432-840
2025-07-10 23:52:18,383 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-07-10 23:52:18,384 INFO: [33mPress CTRL+C to quit[0m
2025-07-10 23:54:14,543 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-07-10 23:54:14,546 INFO: [33mPress CTRL+C to quit[0m
2025-07-10 23:56:11,938 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-07-10 23:56:11,942 INFO: [33mPress CTRL+C to quit[0m
2025-07-11 00:00:51,653 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-07-11 00:00:51,654 INFO: [33mPress CTRL+C to quit[0m
2025-07-11 00:00:59,852 INFO: 127.0.0.1 - - [11/Jul/2025 00:00:59] "GET / HTTP/1.1" 200 -
2025-07-11 00:01:00,038 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:00] "GET /static/js/app.js HTTP/1.1" 200 -
2025-07-11 00:01:00,039 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:00] "GET /static/css/style.css HTTP/1.1" 200 -
2025-07-11 00:01:00,046 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:00] "GET /uploads/icons/20250617_202240_2.webp HTTP/1.1" 200 -
2025-07-11 00:01:00,049 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:00] "GET /uploads/icons/20250613_015832_2.webp HTTP/1.1" 200 -
2025-07-11 00:01:00,063 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:00] "GET /uploads/icons/20250613_001329_HLk9Vn2d.webp HTTP/1.1" 200 -
2025-07-11 00:01:00,066 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:00] "GET /uploads/icons/20250613_000452_Ou2ESueq.webp HTTP/1.1" 200 -
2025-07-11 00:01:00,097 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:00] "GET /uploads/icons/20250612_235643_q8gEGVAp.webp HTTP/1.1" 200 -
2025-07-11 00:01:00,518 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:00] "GET /uploads/icons/20250612_151120_1.webp HTTP/1.1" 200 -
2025-07-11 00:01:00,526 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:00] "GET /uploads/icons/20250611_221720_1.webp HTTP/1.1" 200 -
2025-07-11 00:01:00,529 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:00] "GET /uploads/icons/20250611_190659_venom.webp HTTP/1.1" 200 -
2025-07-11 00:01:00,538 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:00] "GET /uploads/icons/20250611_191431_2.webp HTTP/1.1" 200 -
2025-07-11 00:01:00,540 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:00] "GET /uploads/icons/20250611_191905_dnspy.webp HTTP/1.1" 200 -
2025-07-11 00:01:02,131 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:02] "GET /static/favicon.ico HTTP/1.1" 200 -
2025-07-11 00:01:02,858 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:02] "[32mGET /app/13 HTTP/1.1[0m" 302 -
2025-07-11 00:01:02,880 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:02] "GET /gate?next=/app/13 HTTP/1.1" 200 -
2025-07-11 00:01:02,919 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-11 00:01:02,921 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:02] "GET /static/js/client.js HTTP/1.1" 200 -
2025-07-11 00:01:02,924 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-11 00:01:02,990 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:02] "GET /fp/nonce HTTP/1.1" 200 -
2025-07-11 00:01:05,051 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:05] "GET / HTTP/1.1" 200 -
2025-07-11 00:01:05,086 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:05] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-11 00:01:05,090 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:05] "[36mGET /uploads/icons/20250617_202240_2.webp HTTP/1.1[0m" 304 -
2025-07-11 00:01:05,094 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:05] "[36mGET /uploads/icons/20250613_015832_2.webp HTTP/1.1[0m" 304 -
2025-07-11 00:01:05,101 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:05] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-11 00:01:05,121 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:05] "[36mGET /uploads/icons/20250613_001329_HLk9Vn2d.webp HTTP/1.1[0m" 304 -
2025-07-11 00:01:05,123 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:05] "[36mGET /uploads/icons/20250613_000452_Ou2ESueq.webp HTTP/1.1[0m" 304 -
2025-07-11 00:01:05,220 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:05] "[36mGET /uploads/icons/20250612_235643_q8gEGVAp.webp HTTP/1.1[0m" 304 -
2025-07-11 00:01:05,287 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:05] "[36mGET /uploads/icons/20250612_151120_1.webp HTTP/1.1[0m" 304 -
2025-07-11 00:01:05,291 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:05] "[36mGET /uploads/icons/20250611_221720_1.webp HTTP/1.1[0m" 304 -
2025-07-11 00:01:05,293 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:05] "[36mGET /uploads/icons/20250611_191905_dnspy.webp HTTP/1.1[0m" 304 -
2025-07-11 00:01:05,297 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:05] "[36mGET /uploads/icons/20250611_190659_venom.webp HTTP/1.1[0m" 304 -
2025-07-11 00:01:05,311 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:05] "[36mGET /uploads/icons/20250611_191431_2.webp HTTP/1.1[0m" 304 -
2025-07-11 00:01:07,350 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:07] "[32mGET /app/13 HTTP/1.1[0m" 302 -
2025-07-11 00:01:07,363 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:07] "GET /gate?next=/app/13 HTTP/1.1" 200 -
2025-07-11 00:01:07,400 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:07] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-11 00:01:07,403 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-11 00:01:07,408 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:07] "[36mGET /static/js/client.js HTTP/1.1[0m" 304 -
2025-07-11 00:01:07,511 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:07] "GET /fp/nonce HTTP/1.1" 200 -
2025-07-11 00:01:08,465 INFO: 127.0.0.1 - - [11/Jul/2025 00:01:08] "POST /fp/collect HTTP/1.1" 200 -
2025-07-11 00:03:37,239 INFO: 127.0.0.1 - - [11/Jul/2025 00:03:37] "GET /app/13 HTTP/1.1" 200 -
2025-07-11 00:03:37,275 INFO: 127.0.0.1 - - [11/Jul/2025 00:03:37] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-11 00:03:37,285 INFO: 127.0.0.1 - - [11/Jul/2025 00:03:37] "GET /uploads/screenshots/20250613_001329_HLk9Vn3d.webp HTTP/1.1" 200 -
2025-07-11 00:03:37,292 INFO: 127.0.0.1 - - [11/Jul/2025 00:03:37] "[36mGET /uploads/icons/20250613_001329_HLk9Vn2d.webp HTTP/1.1[0m" 304 -
2025-07-11 00:03:37,295 INFO: 127.0.0.1 - - [11/Jul/2025 00:03:37] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-11 00:03:37,438 INFO: 127.0.0.1 - - [11/Jul/2025 00:03:37] "GET /fp/nonce HTTP/1.1" 200 -
2025-07-11 00:03:39,089 INFO: 127.0.0.1 - - [11/Jul/2025 00:03:39] "GET /app/13 HTTP/1.1" 200 -
2025-07-11 00:03:39,115 INFO: 127.0.0.1 - - [11/Jul/2025 00:03:39] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-11 00:03:39,116 INFO: 127.0.0.1 - - [11/Jul/2025 00:03:39] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-11 00:03:39,126 INFO: 127.0.0.1 - - [11/Jul/2025 00:03:39] "[36mGET /uploads/icons/20250613_001329_HLk9Vn2d.webp HTTP/1.1[0m" 304 -
2025-07-11 00:03:39,137 INFO: 127.0.0.1 - - [11/Jul/2025 00:03:39] "[36mGET /uploads/screenshots/20250613_001329_HLk9Vn3d.webp HTTP/1.1[0m" 304 -
2025-07-11 00:03:39,151 INFO: 127.0.0.1 - - [11/Jul/2025 00:03:39] "GET /fp/nonce HTTP/1.1" 200 -
2025-07-11 00:03:39,245 INFO: 127.0.0.1 - - [11/Jul/2025 00:03:39] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-11 00:03:49,325 INFO: 127.0.0.1 - - [11/Jul/2025 00:03:49] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-11 00:03:50,267 INFO: 127.0.0.1 - - [11/Jul/2025 00:03:50] "GET /app/13 HTTP/1.1" 200 -
2025-07-11 00:03:50,341 INFO: 127.0.0.1 - - [11/Jul/2025 00:03:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-11 00:03:50,355 INFO: 127.0.0.1 - - [11/Jul/2025 00:03:50] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-11 00:03:50,358 INFO: 127.0.0.1 - - [11/Jul/2025 00:03:50] "[36mGET /uploads/icons/20250613_001329_HLk9Vn2d.webp HTTP/1.1[0m" 304 -
2025-07-11 00:03:50,360 INFO: 127.0.0.1 - - [11/Jul/2025 00:03:50] "[36mGET /uploads/screenshots/20250613_001329_HLk9Vn3d.webp HTTP/1.1[0m" 304 -
2025-07-11 00:03:50,391 INFO: 127.0.0.1 - - [11/Jul/2025 00:03:50] "GET /fp/nonce HTTP/1.1" 200 -
2025-07-11 00:03:50,621 INFO: 127.0.0.1 - - [11/Jul/2025 00:03:50] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-11 00:04:02,371 INFO: 127.0.0.1 - - [11/Jul/2025 00:04:02] "GET /app/13 HTTP/1.1" 200 -
2025-07-11 00:04:02,413 INFO: 127.0.0.1 - - [11/Jul/2025 00:04:02] "GET /static/css/style.css HTTP/1.1" 200 -
2025-07-11 00:04:02,421 INFO: 127.0.0.1 - - [11/Jul/2025 00:04:02] "GET /static/js/app.js HTTP/1.1" 200 -
2025-07-11 00:04:02,427 INFO: 127.0.0.1 - - [11/Jul/2025 00:04:02] "GET /uploads/icons/20250613_001329_HLk9Vn2d.webp HTTP/1.1" 200 -
2025-07-11 00:04:02,432 INFO: 127.0.0.1 - - [11/Jul/2025 00:04:02] "GET /uploads/screenshots/20250613_001329_HLk9Vn3d.webp HTTP/1.1" 200 -
2025-07-11 00:04:02,961 INFO: 127.0.0.1 - - [11/Jul/2025 00:04:02] "GET /fp/nonce HTTP/1.1" 200 -
2025-07-11 00:04:04,070 INFO: 127.0.0.1 - - [11/Jul/2025 00:04:04] "GET /static/favicon.ico HTTP/1.1" 200 -
2025-07-11 00:04:27,287 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-07-11 00:04:27,288 INFO: [33mPress CTRL+C to quit[0m
2025-07-11 00:04:27,292 INFO:  * Restarting with stat
2025-07-11 00:04:28,570 WARNING:  * Debugger is active!
2025-07-11 00:04:28,577 INFO:  * Debugger PIN: 437-432-840
2025-07-11 00:04:32,515 INFO: 127.0.0.1 - - [11/Jul/2025 00:04:32] "GET /app/13 HTTP/1.1" 200 -
2025-07-11 00:04:32,739 INFO: 127.0.0.1 - - [11/Jul/2025 00:04:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-11 00:04:32,744 INFO: 127.0.0.1 - - [11/Jul/2025 00:04:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-11 00:04:32,748 INFO: 127.0.0.1 - - [11/Jul/2025 00:04:32] "[36mGET /uploads/icons/20250613_001329_HLk9Vn2d.webp HTTP/1.1[0m" 304 -
2025-07-11 00:04:32,760 INFO: 127.0.0.1 - - [11/Jul/2025 00:04:32] "[36mGET /uploads/screenshots/20250613_001329_HLk9Vn3d.webp HTTP/1.1[0m" 304 -
2025-07-11 00:04:32,950 INFO: 127.0.0.1 - - [11/Jul/2025 00:04:32] "GET /fp/nonce HTTP/1.1" 200 -
2025-07-11 00:04:32,970 INFO: 127.0.0.1 - - [11/Jul/2025 00:04:32] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-11 00:04:46,094 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-11 00:04:46,204 INFO:  * Restarting with stat
2025-07-11 00:04:47,577 WARNING:  * Debugger is active!
2025-07-11 00:04:47,584 INFO:  * Debugger PIN: 437-432-840
2025-07-11 00:04:48,661 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-11 00:04:48,771 INFO:  * Restarting with stat
2025-07-11 00:04:50,094 WARNING:  * Debugger is active!
2025-07-11 00:04:50,101 INFO:  * Debugger PIN: 437-432-840
2025-07-11 00:04:51,177 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-11 00:04:51,273 INFO:  * Restarting with stat
2025-07-11 00:04:52,606 WARNING:  * Debugger is active!
2025-07-11 00:04:52,613 INFO:  * Debugger PIN: 437-432-840
2025-07-11 00:04:53,682 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-11 00:04:53,784 INFO:  * Restarting with stat
2025-07-11 00:04:55,058 WARNING:  * Debugger is active!
2025-07-11 00:04:55,066 INFO:  * Debugger PIN: 437-432-840
2025-07-11 00:04:58,177 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-11 00:04:58,286 INFO:  * Restarting with stat
2025-07-11 00:04:59,781 WARNING:  * Debugger is active!
2025-07-11 00:04:59,788 INFO:  * Debugger PIN: 437-432-840
2025-07-11 00:05:05,987 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-11 00:05:06,097 INFO:  * Restarting with stat
2025-07-11 00:05:07,521 WARNING:  * Debugger is active!
2025-07-11 00:05:07,527 INFO:  * Debugger PIN: 437-432-840
2025-07-11 00:05:11,566 INFO: 127.0.0.1 - - [11/Jul/2025 00:05:11] "GET /app/13 HTTP/1.1" 200 -
2025-07-11 00:05:11,806 INFO: 127.0.0.1 - - [11/Jul/2025 00:05:11] "[36mGET /uploads/screenshots/20250613_001329_HLk9Vn3d.webp HTTP/1.1[0m" 304 -
2025-07-11 00:05:11,821 INFO: 127.0.0.1 - - [11/Jul/2025 00:05:11] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-11 00:05:11,823 INFO: 127.0.0.1 - - [11/Jul/2025 00:05:11] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-11 00:05:11,826 INFO: 127.0.0.1 - - [11/Jul/2025 00:05:11] "[36mGET /uploads/icons/20250613_001329_HLk9Vn2d.webp HTTP/1.1[0m" 304 -
2025-07-11 00:05:11,967 INFO: 127.0.0.1 - - [11/Jul/2025 00:05:11] "GET /fp/nonce HTTP/1.1" 200 -
2025-07-11 00:05:12,127 INFO: 127.0.0.1 - - [11/Jul/2025 00:05:12] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-11 00:07:17,431 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-11 00:07:17,636 INFO:  * Restarting with stat
2025-07-11 00:07:19,157 WARNING:  * Debugger is active!
2025-07-11 00:07:19,168 INFO:  * Debugger PIN: 437-432-840
2025-07-11 00:07:19,482 INFO: 127.0.0.1 - - [11/Jul/2025 00:07:19] "GET /app/13 HTTP/1.1" 200 -
2025-07-11 00:07:19,687 INFO: 127.0.0.1 - - [11/Jul/2025 00:07:19] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-11 00:07:19,690 INFO: 127.0.0.1 - - [11/Jul/2025 00:07:19] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-11 00:07:19,697 INFO: 127.0.0.1 - - [11/Jul/2025 00:07:19] "[36mGET /uploads/icons/20250613_001329_HLk9Vn2d.webp HTTP/1.1[0m" 304 -
2025-07-11 00:07:19,699 INFO: 127.0.0.1 - - [11/Jul/2025 00:07:19] "[36mGET /uploads/screenshots/20250613_001329_HLk9Vn3d.webp HTTP/1.1[0m" 304 -
2025-07-11 00:07:19,755 INFO: 127.0.0.1 - - [11/Jul/2025 00:07:19] "GET /fp/nonce HTTP/1.1" 200 -
2025-07-11 00:07:19,822 INFO: 127.0.0.1 - - [11/Jul/2025 00:07:19] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-11 00:09:57,696 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-11 00:09:57,822 INFO:  * Restarting with stat
2025-07-11 00:09:59,390 WARNING:  * Debugger is active!
2025-07-11 00:09:59,397 INFO:  * Debugger PIN: 437-432-840
2025-07-11 00:10:38,343 INFO: 127.0.0.1 - - [11/Jul/2025 00:10:38] "GET / HTTP/1.1" 200 -
2025-07-11 00:10:38,607 INFO: 127.0.0.1 - - [11/Jul/2025 00:10:38] "[36mGET /uploads/icons/20250617_202240_2.webp HTTP/1.1[0m" 304 -
2025-07-11 00:10:38,610 INFO: 127.0.0.1 - - [11/Jul/2025 00:10:38] "[36mGET /uploads/icons/20250613_015832_2.webp HTTP/1.1[0m" 304 -
2025-07-11 00:10:38,632 INFO: 127.0.0.1 - - [11/Jul/2025 00:10:38] "[36mGET /uploads/icons/20250611_191431_2.webp HTTP/1.1[0m" 304 -
2025-07-11 00:10:38,632 INFO: 127.0.0.1 - - [11/Jul/2025 00:10:38] "[36mGET /uploads/icons/20250611_191905_dnspy.webp HTTP/1.1[0m" 304 -
2025-07-11 00:10:38,654 INFO: 127.0.0.1 - - [11/Jul/2025 00:10:38] "[36mGET /uploads/icons/20250613_000452_Ou2ESueq.webp HTTP/1.1[0m" 304 -
2025-07-11 00:10:38,666 INFO: 127.0.0.1 - - [11/Jul/2025 00:10:38] "[36mGET /uploads/icons/20250612_151120_1.webp HTTP/1.1[0m" 304 -
2025-07-11 00:10:38,666 INFO: 127.0.0.1 - - [11/Jul/2025 00:10:38] "[36mGET /uploads/icons/20250611_221720_1.webp HTTP/1.1[0m" 304 -
2025-07-11 00:10:38,673 INFO: 127.0.0.1 - - [11/Jul/2025 00:10:38] "[36mGET /uploads/icons/20250612_235643_q8gEGVAp.webp HTTP/1.1[0m" 304 -
2025-07-11 00:10:38,673 INFO: 127.0.0.1 - - [11/Jul/2025 00:10:38] "[36mGET /uploads/icons/20250611_190659_venom.webp HTTP/1.1[0m" 304 -
2025-07-11 00:10:38,728 INFO: 127.0.0.1 - - [11/Jul/2025 00:10:38] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-11 00:10:55,557 INFO: 127.0.0.1 - - [11/Jul/2025 00:10:55] "GET /app/6 HTTP/1.1" 200 -
2025-07-11 00:10:55,582 INFO: 127.0.0.1 - - [11/Jul/2025 00:10:55] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-11 00:10:55,600 INFO: 127.0.0.1 - - [11/Jul/2025 00:10:55] "[36mGET /uploads/icons/20250611_191431_2.webp HTTP/1.1[0m" 304 -
2025-07-11 00:10:55,600 INFO: 127.0.0.1 - - [11/Jul/2025 00:10:55] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-11 00:10:55,624 INFO: 127.0.0.1 - - [11/Jul/2025 00:10:55] "GET /uploads/screenshots/20250611_191431_fdsgsdgsdfgsdhsh.webp HTTP/1.1" 200 -
2025-07-11 00:10:55,666 INFO: 127.0.0.1 - - [11/Jul/2025 00:10:55] "GET /fp/nonce HTTP/1.1" 200 -
2025-07-11 00:12:37,462 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-11 00:12:37,577 INFO:  * Restarting with stat
2025-07-11 00:12:39,269 WARNING:  * Debugger is active!
2025-07-11 00:12:39,276 INFO:  * Debugger PIN: 437-432-840
2025-07-11 00:12:41,391 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-11 00:12:41,552 INFO:  * Restarting with stat
2025-07-11 00:12:44,895 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-07-11 00:12:44,896 INFO: [33mPress CTRL+C to quit[0m
2025-07-11 00:12:44,899 INFO:  * Restarting with stat
2025-07-11 00:12:46,238 WARNING:  * Debugger is active!
2025-07-11 00:12:46,247 INFO:  * Debugger PIN: 437-432-840
2025-07-11 00:13:45,258 INFO: 127.0.0.1 - - [11/Jul/2025 00:13:45] "GET /app/6 HTTP/1.1" 200 -
2025-07-11 00:13:45,441 INFO: 127.0.0.1 - - [11/Jul/2025 00:13:45] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-11 00:13:45,441 INFO: 127.0.0.1 - - [11/Jul/2025 00:13:45] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-11 00:13:45,460 INFO: 127.0.0.1 - - [11/Jul/2025 00:13:45] "[36mGET /uploads/screenshots/20250611_191431_fdsgsdgsdfgsdhsh.webp HTTP/1.1[0m" 304 -
2025-07-11 00:13:45,460 INFO: 127.0.0.1 - - [11/Jul/2025 00:13:45] "[36mGET /uploads/icons/20250611_191431_2.webp HTTP/1.1[0m" 304 -
2025-07-11 00:13:45,478 INFO: 127.0.0.1 - - [11/Jul/2025 00:13:45] "GET /fp/nonce HTTP/1.1" 200 -
2025-07-11 00:13:45,725 INFO: 127.0.0.1 - - [11/Jul/2025 00:13:45] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-11 00:15:01,385 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-11 00:15:01,543 INFO:  * Restarting with stat
2025-07-11 00:15:02,987 WARNING:  * Debugger is active!
2025-07-11 00:15:02,995 INFO:  * Debugger PIN: 437-432-840
2025-07-11 00:15:09,199 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-11 00:15:09,305 INFO:  * Restarting with stat
2025-07-11 00:15:10,897 WARNING:  * Debugger is active!
2025-07-11 00:15:10,907 INFO:  * Debugger PIN: 437-432-840
2025-07-11 00:15:11,255 INFO: 127.0.0.1 - - [11/Jul/2025 00:15:11] "GET /app/6 HTTP/1.1" 200 -
2025-07-11 00:15:11,455 INFO: 127.0.0.1 - - [11/Jul/2025 00:15:11] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-11 00:15:11,465 INFO: 127.0.0.1 - - [11/Jul/2025 00:15:11] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-11 00:15:11,476 INFO: 127.0.0.1 - - [11/Jul/2025 00:15:11] "[36mGET /uploads/screenshots/20250611_191431_fdsgsdgsdfgsdhsh.webp HTTP/1.1[0m" 304 -
2025-07-11 00:15:11,480 INFO: 127.0.0.1 - - [11/Jul/2025 00:15:11] "[36mGET /uploads/icons/20250611_191431_2.webp HTTP/1.1[0m" 304 -
2025-07-11 00:15:11,498 INFO: 127.0.0.1 - - [11/Jul/2025 00:15:11] "GET /fp/nonce HTTP/1.1" 200 -
2025-07-11 00:15:11,912 INFO: 127.0.0.1 - - [11/Jul/2025 00:15:11] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-11 00:18:42,568 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-11 00:18:42,692 INFO:  * Restarting with stat
2025-07-12 22:21:37,334 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-12 22:21:37,339 INFO: [33mPress CTRL+C to quit[0m
2025-07-12 22:21:45,159 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-12 22:21:45,159 INFO: [33mPress CTRL+C to quit[0m
2025-07-12 22:21:45,164 INFO:  * Restarting with stat
2025-07-12 22:21:46,997 WARNING:  * Debugger is active!
2025-07-12 22:21:47,013 INFO:  * Debugger PIN: 437-432-840
2025-07-12 22:22:14,984 INFO: 127.0.0.1 - - [12/Jul/2025 22:22:14] "GET / HTTP/1.1" 200 -
2025-07-12 22:22:15,221 INFO: 127.0.0.1 - - [12/Jul/2025 22:22:15] "GET /static/css/style.css HTTP/1.1" 200 -
2025-07-12 22:22:15,351 INFO: 127.0.0.1 - - [12/Jul/2025 22:22:15] "GET /static/js/app.js HTTP/1.1" 200 -
2025-07-12 22:22:15,352 INFO: 127.0.0.1 - - [12/Jul/2025 22:22:15] "GET /uploads/icons/20250613_015832_2.webp HTTP/1.1" 200 -
2025-07-12 22:22:15,366 INFO: 127.0.0.1 - - [12/Jul/2025 22:22:15] "GET /uploads/icons/20250617_202240_2.webp HTTP/1.1" 200 -
2025-07-12 22:22:15,381 INFO: 127.0.0.1 - - [12/Jul/2025 22:22:15] "GET /uploads/icons/20250613_001329_HLk9Vn2d.webp HTTP/1.1" 200 -
2025-07-12 22:22:15,681 INFO: 127.0.0.1 - - [12/Jul/2025 22:22:15] "GET /uploads/icons/20250613_000452_Ou2ESueq.webp HTTP/1.1" 200 -
2025-07-12 22:22:15,698 INFO: 127.0.0.1 - - [12/Jul/2025 22:22:15] "GET /uploads/icons/20250612_235643_q8gEGVAp.webp HTTP/1.1" 200 -
2025-07-12 22:22:16,059 INFO: 127.0.0.1 - - [12/Jul/2025 22:22:16] "GET /uploads/icons/20250612_151120_1.webp HTTP/1.1" 200 -
2025-07-12 22:22:16,096 INFO: 127.0.0.1 - - [12/Jul/2025 22:22:16] "GET /uploads/icons/20250611_221720_1.webp HTTP/1.1" 200 -
2025-07-12 22:22:16,108 INFO: 127.0.0.1 - - [12/Jul/2025 22:22:16] "GET /uploads/icons/20250611_191905_dnspy.webp HTTP/1.1" 200 -
2025-07-12 22:22:16,110 INFO: 127.0.0.1 - - [12/Jul/2025 22:22:16] "GET /uploads/icons/20250611_191431_2.webp HTTP/1.1" 200 -
2025-07-12 22:22:16,126 INFO: 127.0.0.1 - - [12/Jul/2025 22:22:16] "GET /uploads/icons/20250611_190659_venom.webp HTTP/1.1" 200 -
2025-07-12 22:22:16,619 INFO: 127.0.0.1 - - [12/Jul/2025 22:22:16] "GET /static/favicon.ico HTTP/1.1" 200 -
2025-07-12 22:25:19,846 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\main.py', reloading
2025-07-12 22:25:20,063 INFO:  * Restarting with stat
2025-07-12 22:25:22,649 WARNING:  * Debugger is active!
2025-07-12 22:25:22,663 INFO:  * Debugger PIN: 437-432-840
2025-07-12 22:25:33,005 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\main.py', reloading
2025-07-12 22:25:33,243 INFO:  * Restarting with stat
2025-07-12 22:25:35,425 WARNING:  * Debugger is active!
2025-07-12 22:25:35,434 INFO:  * Debugger PIN: 437-432-840
2025-07-12 22:28:48,401 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:48] "GET / HTTP/1.1" 200 -
2025-07-12 22:28:48,637 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-12 22:28:48,811 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:48] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-12 22:28:48,826 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:48] "[36mGET /uploads/icons/20250617_202240_2.webp HTTP/1.1[0m" 304 -
2025-07-12 22:28:48,841 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:48] "[36mGET /uploads/icons/20250613_001329_HLk9Vn2d.webp HTTP/1.1[0m" 304 -
2025-07-12 22:28:48,842 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:48] "[36mGET /uploads/icons/20250613_015832_2.webp HTTP/1.1[0m" 304 -
2025-07-12 22:28:49,144 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:49] "[36mGET /uploads/icons/20250611_190659_venom.webp HTTP/1.1[0m" 304 -
2025-07-12 22:28:49,147 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:49] "[36mGET /uploads/icons/20250612_235643_q8gEGVAp.webp HTTP/1.1[0m" 304 -
2025-07-12 22:28:49,155 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:49] "[36mGET /uploads/icons/20250612_151120_1.webp HTTP/1.1[0m" 304 -
2025-07-12 22:28:49,164 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:49] "[36mGET /uploads/icons/20250613_000452_Ou2ESueq.webp HTTP/1.1[0m" 304 -
2025-07-12 22:28:49,174 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:49] "[36mGET /uploads/icons/20250611_221720_1.webp HTTP/1.1[0m" 304 -
2025-07-12 22:28:49,180 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:49] "[36mGET /uploads/icons/20250611_191905_dnspy.webp HTTP/1.1[0m" 304 -
2025-07-12 22:28:49,471 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:49] "[36mGET /uploads/icons/20250611_191431_2.webp HTTP/1.1[0m" 304 -
2025-07-12 22:28:49,803 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:49] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-12 22:28:51,245 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:51] "[32mGET /donation HTTP/1.1[0m" 302 -
2025-07-12 22:28:51,568 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:51] "GET /gate?next=/ HTTP/1.1" 200 -
2025-07-12 22:28:51,824 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:51] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-12 22:28:51,938 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:51] "GET /static/js/client.js HTTP/1.1" 200 -
2025-07-12 22:28:51,967 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:51] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-12 22:28:51,985 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:51] "GET /fp/nonce HTTP/1.1" 200 -
2025-07-12 22:28:53,426 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:53] "GET / HTTP/1.1" 200 -
2025-07-12 22:28:53,671 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:53] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-12 22:28:53,768 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:53] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-12 22:28:53,780 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:53] "[36mGET /uploads/icons/20250613_015832_2.webp HTTP/1.1[0m" 304 -
2025-07-12 22:28:53,787 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:53] "[36mGET /uploads/icons/20250613_001329_HLk9Vn2d.webp HTTP/1.1[0m" 304 -
2025-07-12 22:28:53,795 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:53] "[36mGET /uploads/icons/20250617_202240_2.webp HTTP/1.1[0m" 304 -
2025-07-12 22:28:54,097 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:54] "[36mGET /uploads/icons/20250613_000452_Ou2ESueq.webp HTTP/1.1[0m" 304 -
2025-07-12 22:28:54,100 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:54] "[36mGET /uploads/icons/20250611_221720_1.webp HTTP/1.1[0m" 304 -
2025-07-12 22:28:54,113 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:54] "[36mGET /uploads/icons/20250612_151120_1.webp HTTP/1.1[0m" 304 -
2025-07-12 22:28:54,116 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:54] "[36mGET /uploads/icons/20250612_235643_q8gEGVAp.webp HTTP/1.1[0m" 304 -
2025-07-12 22:28:54,119 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:54] "[36mGET /uploads/icons/20250611_191905_dnspy.webp HTTP/1.1[0m" 304 -
2025-07-12 22:28:54,125 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:54] "[36mGET /uploads/icons/20250611_191431_2.webp HTTP/1.1[0m" 304 -
2025-07-12 22:28:54,418 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:54] "[36mGET /uploads/icons/20250611_190659_venom.webp HTTP/1.1[0m" 304 -
2025-07-12 22:28:54,966 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:54] "[32mGET /donation HTTP/1.1[0m" 302 -
2025-07-12 22:28:55,219 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:55] "GET /gate?next=/ HTTP/1.1" 200 -
2025-07-12 22:28:55,310 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:55] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-12 22:28:55,623 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:55] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-12 22:28:55,639 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:55] "[36mGET /static/js/client.js HTTP/1.1[0m" 304 -
2025-07-12 22:28:56,153 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:56] "GET /fp/nonce HTTP/1.1" 200 -
2025-07-12 22:28:57,077 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:57] "POST /fp/collect HTTP/1.1" 200 -
2025-07-12 22:28:57,952 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:57] "GET / HTTP/1.1" 200 -
2025-07-12 22:28:58,182 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:58] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-12 22:28:58,339 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:58] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-12 22:28:58,357 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:58] "[36mGET /uploads/icons/20250617_202240_2.webp HTTP/1.1[0m" 304 -
2025-07-12 22:28:58,366 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:58] "[36mGET /uploads/icons/20250613_001329_HLk9Vn2d.webp HTTP/1.1[0m" 304 -
2025-07-12 22:28:58,376 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:58] "[36mGET /uploads/icons/20250613_015832_2.webp HTTP/1.1[0m" 304 -
2025-07-12 22:28:58,673 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:58] "[36mGET /uploads/icons/20250612_151120_1.webp HTTP/1.1[0m" 304 -
2025-07-12 22:28:58,676 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:58] "[36mGET /uploads/icons/20250613_000452_Ou2ESueq.webp HTTP/1.1[0m" 304 -
2025-07-12 22:28:58,685 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:58] "[36mGET /uploads/icons/20250611_221720_1.webp HTTP/1.1[0m" 304 -
2025-07-12 22:28:58,706 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:58] "[36mGET /uploads/icons/20250611_191905_dnspy.webp HTTP/1.1[0m" 304 -
2025-07-12 22:28:58,714 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:58] "[36mGET /uploads/icons/20250612_235643_q8gEGVAp.webp HTTP/1.1[0m" 304 -
2025-07-12 22:28:58,737 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:58] "[36mGET /uploads/icons/20250611_191431_2.webp HTTP/1.1[0m" 304 -
2025-07-12 22:28:58,999 INFO: 127.0.0.1 - - [12/Jul/2025 22:28:58] "[36mGET /uploads/icons/20250611_190659_venom.webp HTTP/1.1[0m" 304 -
2025-07-12 22:29:00,645 INFO: 127.0.0.1 - - [12/Jul/2025 22:29:00] "GET /donation HTTP/1.1" 200 -
2025-07-12 22:29:00,878 INFO: 127.0.0.1 - - [12/Jul/2025 22:29:00] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-12 22:29:01,040 INFO: 127.0.0.1 - - [12/Jul/2025 22:29:01] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-12 22:29:01,066 INFO: 127.0.0.1 - - [12/Jul/2025 22:29:01] "[33mGET /static/tut.mp4 HTTP/1.1[0m" 404 -
2025-07-12 22:29:07,359 INFO: 127.0.0.1 - - [12/Jul/2025 22:29:07] "GET /suggestions HTTP/1.1" 200 -
2025-07-12 22:29:07,606 INFO: 127.0.0.1 - - [12/Jul/2025 22:29:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-12 22:29:07,709 INFO: 127.0.0.1 - - [12/Jul/2025 22:29:07] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-12 22:29:09,123 INFO: 127.0.0.1 - - [12/Jul/2025 22:29:09] "GET /suggestions HTTP/1.1" 200 -
2025-07-12 22:29:09,469 INFO: 127.0.0.1 - - [12/Jul/2025 22:29:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-12 22:29:09,481 INFO: 127.0.0.1 - - [12/Jul/2025 22:29:09] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-12 22:29:10,324 INFO: 127.0.0.1 - - [12/Jul/2025 22:29:10] "GET / HTTP/1.1" 200 -
2025-07-12 22:29:10,676 INFO: 127.0.0.1 - - [12/Jul/2025 22:29:10] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-12 22:29:10,678 INFO: 127.0.0.1 - - [12/Jul/2025 22:29:10] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-12 22:29:10,693 INFO: 127.0.0.1 - - [12/Jul/2025 22:29:10] "[36mGET /uploads/icons/20250613_015832_2.webp HTTP/1.1[0m" 304 -
2025-07-12 22:29:10,697 INFO: 127.0.0.1 - - [12/Jul/2025 22:29:10] "[36mGET /uploads/icons/20250617_202240_2.webp HTTP/1.1[0m" 304 -
2025-07-12 22:29:10,941 INFO: 127.0.0.1 - - [12/Jul/2025 22:29:10] "[36mGET /uploads/icons/20250613_001329_HLk9Vn2d.webp HTTP/1.1[0m" 304 -
2025-07-12 22:29:11,025 INFO: 127.0.0.1 - - [12/Jul/2025 22:29:11] "[36mGET /uploads/icons/20250611_221720_1.webp HTTP/1.1[0m" 304 -
2025-07-12 22:29:11,030 INFO: 127.0.0.1 - - [12/Jul/2025 22:29:11] "[36mGET /uploads/icons/20250611_191905_dnspy.webp HTTP/1.1[0m" 304 -
2025-07-12 22:29:11,035 INFO: 127.0.0.1 - - [12/Jul/2025 22:29:11] "[36mGET /uploads/icons/20250612_151120_1.webp HTTP/1.1[0m" 304 -
2025-07-12 22:29:11,037 INFO: 127.0.0.1 - - [12/Jul/2025 22:29:11] "[36mGET /uploads/icons/20250613_000452_Ou2ESueq.webp HTTP/1.1[0m" 304 -
2025-07-12 22:29:11,059 INFO: 127.0.0.1 - - [12/Jul/2025 22:29:11] "[36mGET /uploads/icons/20250612_235643_q8gEGVAp.webp HTTP/1.1[0m" 304 -
2025-07-12 22:29:11,269 INFO: 127.0.0.1 - - [12/Jul/2025 22:29:11] "[36mGET /uploads/icons/20250611_191431_2.webp HTTP/1.1[0m" 304 -
2025-07-12 22:29:11,400 INFO: 127.0.0.1 - - [12/Jul/2025 22:29:11] "[36mGET /uploads/icons/20250611_190659_venom.webp HTTP/1.1[0m" 304 -
2025-07-12 22:30:21,190 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\auth.py', reloading
2025-07-12 22:30:21,351 INFO:  * Restarting with stat
2025-07-12 22:30:23,405 WARNING:  * Debugger is active!
2025-07-12 22:30:23,414 INFO:  * Debugger PIN: 437-432-840
2025-07-12 22:30:24,510 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\auth.py', reloading
2025-07-12 22:30:24,725 INFO:  * Restarting with stat
2025-07-12 22:30:27,117 WARNING:  * Debugger is active!
2025-07-12 22:30:27,128 INFO:  * Debugger PIN: 437-432-840
2025-07-12 22:30:27,261 INFO: 127.0.0.1 - - [12/Jul/2025 22:30:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-12 22:30:33,590 INFO: 127.0.0.1 - - [12/Jul/2025 22:30:33] "[32mGET /donation HTTP/1.1[0m" 302 -
2025-07-12 22:30:33,953 INFO: 127.0.0.1 - - [12/Jul/2025 22:30:33] "GET /gate?next=/donation HTTP/1.1" 200 -
2025-07-12 22:30:34,418 INFO: 127.0.0.1 - - [12/Jul/2025 22:30:34] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-12 22:30:34,733 INFO: 127.0.0.1 - - [12/Jul/2025 22:30:34] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-12 22:30:34,747 INFO: 127.0.0.1 - - [12/Jul/2025 22:30:34] "[36mGET /static/js/client.js HTTP/1.1[0m" 304 -
2025-07-12 22:30:34,998 INFO: 127.0.0.1 - - [12/Jul/2025 22:30:34] "GET /fp/nonce HTTP/1.1" 200 -
2025-07-12 22:30:35,651 INFO: 127.0.0.1 - - [12/Jul/2025 22:30:35] "POST /fp/collect HTTP/1.1" 200 -
2025-07-12 22:30:36,514 INFO: 127.0.0.1 - - [12/Jul/2025 22:30:36] "GET /donation HTTP/1.1" 200 -
2025-07-12 22:30:36,754 INFO: 127.0.0.1 - - [12/Jul/2025 22:30:36] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-12 22:30:36,891 INFO: 127.0.0.1 - - [12/Jul/2025 22:30:36] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-12 22:30:36,974 INFO: 127.0.0.1 - - [12/Jul/2025 22:30:36] "[33mGET /static/tut.mp4 HTTP/1.1[0m" 404 -
2025-07-12 22:49:34,791 INFO: 127.0.0.1 - - [12/Jul/2025 22:49:34] "GET /donation HTTP/1.1" 200 -
2025-07-12 22:49:35,024 INFO: 127.0.0.1 - - [12/Jul/2025 22:49:35] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-12 22:49:35,241 INFO: 127.0.0.1 - - [12/Jul/2025 22:49:35] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-12 22:49:35,425 INFO: 127.0.0.1 - - [12/Jul/2025 22:49:35] "[33mGET /static/tut.mp4 HTTP/1.1[0m" 404 -
2025-07-12 22:49:35,847 INFO: 127.0.0.1 - - [12/Jul/2025 22:49:35] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-12 23:11:16,171 INFO: 127.0.0.1 - - [12/Jul/2025 23:11:16] "GET /donation HTTP/1.1" 200 -
2025-07-12 23:11:16,406 INFO: 127.0.0.1 - - [12/Jul/2025 23:11:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-12 23:11:16,563 INFO: 127.0.0.1 - - [12/Jul/2025 23:11:16] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-12 23:11:16,615 INFO: 127.0.0.1 - - [12/Jul/2025 23:11:16] "[35m[1mGET /static/tut.mp4 HTTP/1.1[0m" 206 -
2025-07-12 23:11:17,171 INFO: 127.0.0.1 - - [12/Jul/2025 23:11:17] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-16 07:24:53,091 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-07-16 07:24:53,092 INFO: [33mPress CTRL+C to quit[0m
2025-07-16 07:24:53,097 INFO:  * Restarting with stat
2025-07-16 07:24:55,143 WARNING:  * Debugger is active!
2025-07-16 07:24:55,151 INFO:  * Debugger PIN: 437-432-840
2025-07-16 07:25:02,463 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\publisher.py', reloading
2025-07-16 07:25:02,739 INFO:  * Restarting with stat
2025-07-16 07:25:04,985 WARNING:  * Debugger is active!
2025-07-16 07:25:04,992 INFO:  * Debugger PIN: 437-432-840
2025-07-16 07:25:05,105 INFO: 127.0.0.1 - - [16/Jul/2025 07:25:05] "GET / HTTP/1.1" 200 -
2025-07-16 07:25:05,405 INFO: 127.0.0.1 - - [16/Jul/2025 07:25:05] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-16 07:25:05,434 INFO: 127.0.0.1 - - [16/Jul/2025 07:25:05] "[36mGET /uploads/icons/20250613_015832_2.webp HTTP/1.1[0m" 304 -
2025-07-16 07:25:05,441 INFO: 127.0.0.1 - - [16/Jul/2025 07:25:05] "[36mGET /uploads/icons/20250617_202240_2.webp HTTP/1.1[0m" 304 -
2025-07-16 07:25:05,444 INFO: 127.0.0.1 - - [16/Jul/2025 07:25:05] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-16 07:25:05,463 INFO: 127.0.0.1 - - [16/Jul/2025 07:25:05] "[36mGET /uploads/icons/20250613_000452_Ou2ESueq.webp HTTP/1.1[0m" 304 -
2025-07-16 07:25:05,470 INFO: 127.0.0.1 - - [16/Jul/2025 07:25:05] "[36mGET /uploads/icons/20250613_001329_HLk9Vn2d.webp HTTP/1.1[0m" 304 -
2025-07-16 07:25:05,494 INFO: 127.0.0.1 - - [16/Jul/2025 07:25:05] "[36mGET /uploads/icons/20250612_235643_q8gEGVAp.webp HTTP/1.1[0m" 304 -
2025-07-16 07:25:05,695 INFO: 127.0.0.1 - - [16/Jul/2025 07:25:05] "[36mGET /uploads/icons/20250612_151120_1.webp HTTP/1.1[0m" 304 -
2025-07-16 07:25:05,717 INFO: 127.0.0.1 - - [16/Jul/2025 07:25:05] "[36mGET /uploads/icons/20250611_221720_1.webp HTTP/1.1[0m" 304 -
2025-07-16 07:25:05,731 INFO: 127.0.0.1 - - [16/Jul/2025 07:25:05] "[36mGET /uploads/icons/20250611_191905_dnspy.webp HTTP/1.1[0m" 304 -
2025-07-16 07:25:05,747 INFO: 127.0.0.1 - - [16/Jul/2025 07:25:05] "[36mGET /uploads/icons/20250611_190659_venom.webp HTTP/1.1[0m" 304 -
2025-07-16 07:25:05,755 INFO: 127.0.0.1 - - [16/Jul/2025 07:25:05] "[36mGET /uploads/icons/20250611_191431_2.webp HTTP/1.1[0m" 304 -
2025-07-16 07:25:16,391 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\admin.py', reloading
2025-07-16 07:25:16,592 INFO:  * Restarting with stat
2025-07-16 07:25:19,426 WARNING:  * Debugger is active!
2025-07-16 07:25:19,435 INFO:  * Debugger PIN: 437-432-840
2025-07-16 07:25:32,819 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\admin.py', reloading
2025-07-16 07:25:33,028 INFO:  * Restarting with stat
2025-07-16 07:25:35,404 WARNING:  * Debugger is active!
2025-07-16 07:25:35,410 INFO:  * Debugger PIN: 437-432-840
2025-07-16 07:25:41,605 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\admin.py', reloading
2025-07-16 07:25:41,846 INFO:  * Restarting with stat
2025-07-16 07:25:44,056 WARNING:  * Debugger is active!
2025-07-16 07:25:44,063 INFO:  * Debugger PIN: 437-432-840
2025-07-16 07:25:56,464 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\admin.py', reloading
2025-07-16 07:25:56,634 INFO:  * Restarting with stat
2025-07-16 07:25:58,707 WARNING:  * Debugger is active!
2025-07-16 07:25:58,714 INFO:  * Debugger PIN: 437-432-840
2025-07-16 07:26:06,995 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\admin.py', reloading
2025-07-16 07:26:07,168 INFO:  * Restarting with stat
2025-07-16 07:26:09,131 WARNING:  * Debugger is active!
2025-07-16 07:26:09,136 INFO:  * Debugger PIN: 437-432-840
2025-07-16 07:27:37,166 INFO: 127.0.0.1 - - [16/Jul/2025 07:27:37] "GET /auth/login HTTP/1.1" 200 -
2025-07-16 07:27:37,454 INFO: 127.0.0.1 - - [16/Jul/2025 07:27:37] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-16 07:27:37,468 INFO: 127.0.0.1 - - [16/Jul/2025 07:27:37] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-16 07:42:12,911 INFO: 127.0.0.1 - - [16/Jul/2025 07:42:12] "GET / HTTP/1.1" 200 -
2025-07-16 07:42:13,025 INFO: 127.0.0.1 - - [16/Jul/2025 07:42:13] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-16 07:42:13,040 INFO: 127.0.0.1 - - [16/Jul/2025 07:42:13] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-16 07:42:13,043 INFO: 127.0.0.1 - - [16/Jul/2025 07:42:13] "[36mGET /uploads/icons/20250617_202240_2.webp HTTP/1.1[0m" 304 -
2025-07-16 07:42:13,053 INFO: 127.0.0.1 - - [16/Jul/2025 07:42:13] "[36mGET /uploads/icons/20250613_015832_2.webp HTTP/1.1[0m" 304 -
2025-07-16 07:42:13,140 INFO: 127.0.0.1 - - [16/Jul/2025 07:42:13] "[36mGET /uploads/icons/20250613_001329_HLk9Vn2d.webp HTTP/1.1[0m" 304 -
2025-07-16 07:42:13,168 INFO: 127.0.0.1 - - [16/Jul/2025 07:42:13] "[36mGET /uploads/icons/20250612_151120_1.webp HTTP/1.1[0m" 304 -
2025-07-16 07:42:13,172 INFO: 127.0.0.1 - - [16/Jul/2025 07:42:13] "[36mGET /uploads/icons/20250611_191905_dnspy.webp HTTP/1.1[0m" 304 -
2025-07-16 07:42:13,177 INFO: 127.0.0.1 - - [16/Jul/2025 07:42:13] "[36mGET /uploads/icons/20250611_191431_2.webp HTTP/1.1[0m" 304 -
2025-07-16 07:42:13,181 INFO: 127.0.0.1 - - [16/Jul/2025 07:42:13] "[36mGET /uploads/icons/20250613_000452_Ou2ESueq.webp HTTP/1.1[0m" 304 -
2025-07-16 07:42:13,184 INFO: 127.0.0.1 - - [16/Jul/2025 07:42:13] "[36mGET /uploads/icons/20250612_235643_q8gEGVAp.webp HTTP/1.1[0m" 304 -
2025-07-16 07:42:13,194 INFO: 127.0.0.1 - - [16/Jul/2025 07:42:13] "[36mGET /uploads/icons/20250611_221720_1.webp HTTP/1.1[0m" 304 -
2025-07-16 07:42:13,206 INFO: 127.0.0.1 - - [16/Jul/2025 07:42:13] "[36mGET /uploads/icons/20250611_190659_venom.webp HTTP/1.1[0m" 304 -
2025-07-16 07:42:14,196 INFO: 127.0.0.1 - - [16/Jul/2025 07:42:14] "GET /auth/login HTTP/1.1" 200 -
2025-07-16 07:42:14,269 INFO: 127.0.0.1 - - [16/Jul/2025 07:42:14] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-16 07:42:14,271 INFO: 127.0.0.1 - - [16/Jul/2025 07:42:14] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-16 07:42:17,561 INFO: 127.0.0.1 - - [16/Jul/2025 07:42:17] "GET /auth/login HTTP/1.1" 200 -
2025-07-16 07:42:17,619 INFO: 127.0.0.1 - - [16/Jul/2025 07:42:17] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-16 07:42:17,631 INFO: 127.0.0.1 - - [16/Jul/2025 07:42:17] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-16 07:42:18,272 INFO: 127.0.0.1 - - [16/Jul/2025 07:42:18] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
