{% extends "base.html" %}

{% block title %}Ban Management - PEPE Store Admin{% endblock %}

{% block content %}
<div class="container-fluid my-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-shield-x text-danger"></i> Ban Management</h2>
                <div>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#banModal">
                        <i class="bi bi-plus-circle"></i> Ban Fingerprint
                    </button>
                    <a href="{{ url_for('ban.admin_ban_events') }}" class="btn btn-outline-info">
                        <i class="bi bi-clock-history"></i> Ban Events
                    </a>
                </div>
            </div>

            {% if bans %}
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Banned Fingerprints ({{ total }} total)</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>Fingerprint</th>
                                    <th>Reason</th>
                                    <th>Banned By</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for ban in bans %}
                                <tr>
                                    <td>
                                        <code class="text-danger">{{ ban.fingerprint[:16] }}...</code>
                                        <button class="btn btn-sm btn-outline-secondary ms-2" 
                                                onclick="copyToClipboard('{{ ban.fingerprint }}')" 
                                                title="Copy full fingerprint">
                                            <i class="bi bi-clipboard"></i>
                                        </button>
                                    </td>
                                    <td>{{ ban.reason }}</td>
                                    <td>{{ ban.banned_by or 'System' }}</td>
                                    <td>{{ ban.created_at }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('ban.admin_fingerprint_info', fingerprint=ban.fingerprint) }}" 
                                               class="btn btn-outline-info" title="View Details">
                                                <i class="bi bi-info-circle"></i>
                                            </a>
                                            <a href="{{ url_for('ban.admin_unban_fingerprint', fingerprint=ban.fingerprint) }}" 
                                               class="btn btn-outline-success" 
                                               onclick="return confirm('Unban this fingerprint?')" 
                                               title="Unban">
                                                <i class="bi bi-check-circle"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                
                {% if total_pages > 1 %}
                <div class="card-footer">
                    <nav aria-label="Ban list pagination">
                        <ul class="pagination justify-content-center mb-0">
                            {% if page > 1 %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page - 1 }}">Previous</a>
                            </li>
                            {% endif %}
                            
                            {% for p in range(1, total_pages + 1) %}
                            <li class="page-item {{ 'active' if p == page else '' }}">
                                <a class="page-link" href="?page={{ p }}">{{ p }}</a>
                            </li>
                            {% endfor %}
                            
                            {% if page < total_pages %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page + 1 }}">Next</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% endif %}
            </div>
            {% else %}
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i> No banned fingerprints found.
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Ban Modal -->
<div class="modal fade" id="banModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Ban Fingerprint</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="banForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="fingerprint" class="form-label">Fingerprint Hash</label>
                        <input type="text" class="form-control" id="fingerprint" name="fingerprint" 
                               placeholder="Enter fingerprint hash" required>
                        <div class="form-text">64-character SHA-256 hash</div>
                    </div>
                    <div class="mb-3">
                        <label for="reason" class="form-label">Reason</label>
                        <select class="form-select" id="reason" name="reason" required>
                            <option value="">Select reason...</option>
                            <option value="Abuse/Spam">Abuse/Spam</option>
                            <option value="Malicious Activity">Malicious Activity</option>
                            <option value="Terms Violation">Terms Violation</option>
                            <option value="Security Threat">Security Threat</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
                    <div class="mb-3" id="customReasonDiv" style="display: none;">
                        <label for="customReason" class="form-label">Custom Reason</label>
                        <input type="text" class="form-control" id="customReason" name="customReason" 
                               placeholder="Enter custom reason">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Ban Fingerprint</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Show/hide custom reason field
document.getElementById('reason').addEventListener('change', function() {
    const customDiv = document.getElementById('customReasonDiv');
    if (this.value === 'Other') {
        customDiv.style.display = 'block';
        document.getElementById('customReason').required = true;
    } else {
        customDiv.style.display = 'none';
        document.getElementById('customReason').required = false;
    }
});

// Handle ban form submission
document.getElementById('banForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const fingerprint = formData.get('fingerprint');
    let reason = formData.get('reason');
    
    if (reason === 'Other') {
        reason = formData.get('customReason');
    }
    
    if (!fingerprint || !reason) {
        alert('Please fill in all required fields');
        return;
    }
    
    try {
        const response = await fetch('/api/ban', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Visitor-FP': getCookie('fingerprint') || ''
            },
            body: JSON.stringify({
                fingerprint: fingerprint,
                reason: reason
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert('Fingerprint banned successfully');
            location.reload();
        } else {
            alert('Error: ' + result.message);
        }
    } catch (error) {
        alert('Network error: ' + error.message);
    }
});

// Copy to clipboard function
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show temporary success message
        const btn = event.target.closest('button');
        const originalHTML = btn.innerHTML;
        btn.innerHTML = '<i class="bi bi-check text-success"></i>';
        setTimeout(() => {
            btn.innerHTML = originalHTML;
        }, 1000);
    });
}

// Get cookie helper
function getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
}
</script>
{% endblock %}
