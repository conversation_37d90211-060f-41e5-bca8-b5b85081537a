{% extends "base.html" %}

{% block title %}Create Campaign Link - PEPE Store Admin{% endblock %}

{% block content %}
<div class="container my-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0"><i class="bi bi-link-45deg text-success"></i> Create Campaign Link</h4>
                        <a href="{{ url_for('referral.admin_referral_dashboard') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Dashboard
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" id="campaignForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="description" class="form-label">Campaign Description *</label>
                                    <input type="text" class="form-control" id="description" name="description" 
                                           placeholder="Enter campaign description" required>
                                    <div class="form-text">Brief description of this campaign</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="code" class="form-label">Campaign Code</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="code" name="code" 
                                               placeholder="Leave empty to auto-generate">
                                        <button type="button" class="btn btn-outline-secondary" 
                                                onclick="generateCode()" title="Generate random code">
                                            <i class="bi bi-arrow-clockwise"></i>
                                        </button>
                                    </div>
                                    <div class="form-text">Unique code for campaign tracking. Auto-generated if empty.</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">Campaign Link Preview</h6>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="linkPreview" 
                                               value="{{ request.url_root }}?ref=" readonly>
                                        <button type="button" class="btn btn-outline-primary" 
                                                onclick="copyLink()" title="Copy link">
                                            <i class="bi bi-clipboard"></i>
                                        </button>
                                    </div>
                                    <small class="text-muted">This link will be generated after creating the campaign</small>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('referral.admin_referral_dashboard') }}" class="btn btn-secondary">
                                <i class="bi bi-x-circle"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-check-circle"></i> Create Campaign Link
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Campaign Types -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-lightbulb text-warning"></i> Campaign Ideas</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center mb-3">
                                <i class="bi bi-megaphone text-primary" style="font-size: 2rem;"></i>
                                <h6 class="mt-2">Marketing Campaigns</h6>
                                <p class="text-muted small">Track effectiveness of different marketing channels</p>
                                <button class="btn btn-sm btn-outline-primary" onclick="fillExample('Marketing_Q1_2024', 'Q1 2024 Marketing Campaign')">
                                    Use Example
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center mb-3">
                                <i class="bi bi-share text-success" style="font-size: 2rem;"></i>
                                <h6 class="mt-2">Social Media</h6>
                                <p class="text-muted small">Track visitors from social media platforms</p>
                                <button class="btn btn-sm btn-outline-success" onclick="fillExample('Social_Twitter', 'Twitter Social Campaign')">
                                    Use Example
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center mb-3">
                                <i class="bi bi-envelope text-info" style="font-size: 2rem;"></i>
                                <h6 class="mt-2">Email Campaigns</h6>
                                <p class="text-muted small">Track email newsletter effectiveness</p>
                                <button class="btn btn-sm btn-outline-info" onclick="fillExample('Email_Newsletter', 'Email Newsletter Campaign')">
                                    Use Example
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-3">
                        <i class="bi bi-info-circle"></i>
                        <strong>Pro Tip:</strong> Use descriptive campaign codes to easily identify traffic sources in your analytics.
                    </div>
                </div>
            </div>

            <!-- Usage Instructions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-question-circle text-info"></i> How to Use Campaign Links</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>1. Create Campaign Link</h6>
                            <p class="text-muted small">Generate a unique tracking code for your campaign</p>
                            
                            <h6>2. Share the Link</h6>
                            <p class="text-muted small">Use the generated link in your marketing materials</p>
                        </div>
                        <div class="col-md-6">
                            <h6>3. Track Performance</h6>
                            <p class="text-muted small">Monitor unique visitors and conversion rates</p>
                            
                            <h6>4. Analyze Results</h6>
                            <p class="text-muted small">View detailed analytics in the referral dashboard</p>
                        </div>
                    </div>
                    
                    <div class="bg-light p-3 rounded mt-3">
                        <h6>Example Usage:</h6>
                        <code>{{ request.url_root }}?ref=SUMMER_SALE_2024</code>
                        <p class="text-muted small mt-2">This link will track all visitors who come from your summer sale campaign.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Update link preview when code changes
document.getElementById('code').addEventListener('input', function() {
    const baseUrl = '{{ request.url_root }}?ref=';
    const preview = document.getElementById('linkPreview');
    preview.value = baseUrl + (this.value || '[CODE]');
});

// Generate random code
function generateCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let code = 'CAMP_';
    for (let i = 0; i < 8; i++) {
        code += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    document.getElementById('code').value = code;
    document.getElementById('code').dispatchEvent(new Event('input'));
}

// Fill example data
function fillExample(code, description) {
    document.getElementById('code').value = code;
    document.getElementById('description').value = description;
    document.getElementById('code').dispatchEvent(new Event('input'));
}

// Copy link to clipboard
function copyLink() {
    const linkInput = document.getElementById('linkPreview');
    linkInput.select();
    navigator.clipboard.writeText(linkInput.value).then(function() {
        const btn = event.target.closest('button');
        const originalHTML = btn.innerHTML;
        btn.innerHTML = '<i class="bi bi-check text-success"></i>';
        setTimeout(() => {
            btn.innerHTML = originalHTML;
        }, 1000);
        
        showToast('Link copied to clipboard!', 'success');
    });
}

// Form validation
document.getElementById('campaignForm').addEventListener('submit', function(e) {
    const description = document.getElementById('description').value.trim();
    const code = document.getElementById('code').value.trim();
    
    if (!description) {
        e.preventDefault();
        alert('Please enter a campaign description');
        return;
    }
    
    if (code && code.length < 3) {
        e.preventDefault();
        alert('Campaign code must be at least 3 characters long');
        return;
    }
    
    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Creating...';
});

// Toast notification function
function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toast-container') || createToastContainer();
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    toastContainer.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toast-container';
    container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
    document.body.appendChild(container);
    return container;
}

// Initialize link preview
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('code').dispatchEvent(new Event('input'));
});
</script>
{% endblock %}
