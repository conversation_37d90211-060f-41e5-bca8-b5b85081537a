"""
Ban-by-Fingerprint System
Implements fingerprint-based banning that works even when IP/device rotates
Includes admin management interface and ban event logging
"""

from datetime import datetime
from flask import Blueprint, request, jsonify, render_template, redirect, url_for, flash, session, abort
from app.models import get_db
from app.utils.common import get_client_ip
from app.utils.auth import require_admin

ban_bp = Blueprint('ban', __name__)

class BanManager:
    """Manages fingerprint-based banning operations"""
    
    @staticmethod
    def ban_fingerprint(fingerprint, reason, banned_by=None):
        """Ban a fingerprint with reason and admin info"""
        if not fingerprint or not reason:
            return False, "Fingerprint and reason are required"
        
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Check if already banned
            cursor.execute('SELECT 1 FROM banned_fingerprints WHERE fingerprint = ?', (fingerprint,))
            if cursor.fetchone():
                return False, "Fingerprint is already banned"
            
            # Add to banned list
            cursor.execute('''
                INSERT INTO banned_fingerprints (fingerprint, reason, banned_by)
                VALUES (?, ?, ?)
            ''', (fingerprint, reason, banned_by))

            # Log the ban event (using same connection)
            BanEventLogger.log_ban_event_with_cursor(cursor, fingerprint, 'banned', reason, banned_by)

            conn.commit()
            return True, "Fingerprint banned successfully"
    
    @staticmethod
    def unban_fingerprint(fingerprint, unbanned_by=None):
        """Remove fingerprint from ban list"""
        if not fingerprint:
            return False, "Fingerprint is required"
        
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Check if banned
            cursor.execute('SELECT reason FROM banned_fingerprints WHERE fingerprint = ?', (fingerprint,))
            ban_info = cursor.fetchone()
            if not ban_info:
                return False, "Fingerprint is not banned"
            
            # Remove from banned list
            cursor.execute('DELETE FROM banned_fingerprints WHERE fingerprint = ?', (fingerprint,))

            # Log the unban event (using same connection)
            BanEventLogger.log_ban_event_with_cursor(cursor, fingerprint, 'unbanned', f"Unbanned from: {ban_info[0]}", unbanned_by)

            conn.commit()
            return True, "Fingerprint unbanned successfully"
    
    @staticmethod
    def is_banned(fingerprint):
        """Check if fingerprint is banned"""
        if not fingerprint:
            return False
        
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT reason, created_at FROM banned_fingerprints WHERE fingerprint = ?', (fingerprint,))
            result = cursor.fetchone()
            
            if result:
                return {
                    'banned': True,
                    'reason': result[0],
                    'banned_at': result[1]
                }
            return {'banned': False}
    
    @staticmethod
    def get_banned_fingerprints(limit=100, offset=0):
        """Get list of banned fingerprints with pagination"""
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Get total count
            cursor.execute('SELECT COUNT(*) FROM banned_fingerprints')
            total = cursor.fetchone()[0]
            
            # Get paginated results
            cursor.execute('''
                SELECT fingerprint, reason, banned_by, created_at
                FROM banned_fingerprints
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            ''', (limit, offset))
            
            bans = []
            for row in cursor.fetchall():
                bans.append({
                    'fingerprint': row[0],
                    'reason': row[1],
                    'banned_by': row[2],
                    'created_at': row[3]
                })
            
            return {
                'bans': bans,
                'total': total,
                'limit': limit,
                'offset': offset
            }
    
    @staticmethod
    def get_fingerprint_info(fingerprint):
        """Get detailed information about a fingerprint"""
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Get fingerprint data
            cursor.execute('''
                SELECT uuid, created_at, last_seen, visit_count, ip_address, user_agent
                FROM fingerprints WHERE fingerprint = ?
            ''', (fingerprint,))
            fp_data = cursor.fetchone()
            
            # Get ban status
            ban_status = BanManager.is_banned(fingerprint)
            
            # Get recent ban events
            cursor.execute('''
                SELECT event_type, reason, admin_user, created_at
                FROM ban_events WHERE fingerprint = ?
                ORDER BY created_at DESC LIMIT 10
            ''', (fingerprint,))
            events = cursor.fetchall()
            
            return {
                'fingerprint': fingerprint,
                'exists': fp_data is not None,
                'data': {
                    'uuid': fp_data[0] if fp_data else None,
                    'created_at': fp_data[1] if fp_data else None,
                    'last_seen': fp_data[2] if fp_data else None,
                    'visit_count': fp_data[3] if fp_data else None,
                    'ip_address': fp_data[4] if fp_data else None,
                    'user_agent': fp_data[5] if fp_data else None
                },
                'ban_status': ban_status,
                'events': [{'type': e[0], 'reason': e[1], 'admin': e[2], 'timestamp': e[3]} for e in events]
            }

class BanEventLogger:
    """Logs ban-related events for audit trail"""

    @staticmethod
    def log_ban_event_with_cursor(cursor, fingerprint, event_type, reason=None, admin_user=None):
        """Log a ban-related event using existing cursor (no commit)"""
        # Safely get IP and user agent (handle cases outside request context)
        try:
            ip_address = get_client_ip()
            user_agent = request.headers.get('User-Agent', '') if request else ''
        except RuntimeError:
            # Outside request context (e.g., during testing)
            ip_address = 'N/A'
            user_agent = 'N/A'

        cursor.execute('''
            INSERT INTO ban_events (fingerprint, event_type, reason, admin_user, ip_address, user_agent)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (fingerprint, event_type, reason, admin_user, ip_address, user_agent))

    @staticmethod
    def log_ban_event(fingerprint, event_type, reason=None, admin_user=None):
        """Log a ban-related event (standalone - opens own connection)"""
        with get_db() as conn:
            cursor = conn.cursor()

            # Use the cursor-based method to avoid code duplication
            BanEventLogger.log_ban_event_with_cursor(cursor, fingerprint, event_type, reason, admin_user)
            conn.commit()
    
    @staticmethod
    def get_recent_events(limit=50):
        """Get recent ban events for admin dashboard"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT fingerprint, event_type, reason, admin_user, ip_address, created_at
                FROM ban_events
                ORDER BY created_at DESC
                LIMIT ?
            ''', (limit,))
            
            events = []
            for row in cursor.fetchall():
                events.append({
                    'fingerprint': row[0],
                    'event_type': row[1],
                    'reason': row[2],
                    'admin_user': row[3],
                    'ip_address': row[4],
                    'created_at': row[5]
                })
            
            return events

# Admin routes for ban management
@ban_bp.route('/admin/bans')
@require_admin
def admin_ban_list():
    """Admin page to view all banned fingerprints"""
    page = request.args.get('page', 1, type=int)
    per_page = 20
    offset = (page - 1) * per_page
    
    ban_data = BanManager.get_banned_fingerprints(limit=per_page, offset=offset)
    
    return render_template('admin/ban_list.html', 
                         bans=ban_data['bans'],
                         total=ban_data['total'],
                         page=page,
                         per_page=per_page,
                         total_pages=(ban_data['total'] + per_page - 1) // per_page)

@ban_bp.route('/admin/ban/<fingerprint>')
@require_admin
def admin_ban_fingerprint(fingerprint):
    """Admin route to ban a specific fingerprint"""
    reason = request.args.get('reason', 'Banned by admin')
    admin_user = session.get('username', 'Unknown Admin')
    
    success, message = BanManager.ban_fingerprint(fingerprint, reason, admin_user)
    
    if success:
        flash(f'Fingerprint {fingerprint[:16]}... banned successfully', 'success')
    else:
        flash(f'Failed to ban fingerprint: {message}', 'error')
    
    return redirect(url_for('ban.admin_ban_list'))

@ban_bp.route('/admin/unban/<fingerprint>')
@require_admin
def admin_unban_fingerprint(fingerprint):
    """Admin route to unban a specific fingerprint"""
    admin_user = session.get('username', 'Unknown Admin')
    
    success, message = BanManager.unban_fingerprint(fingerprint, admin_user)
    
    if success:
        flash(f'Fingerprint {fingerprint[:16]}... unbanned successfully', 'success')
    else:
        flash(f'Failed to unban fingerprint: {message}', 'error')
    
    return redirect(url_for('ban.admin_ban_list'))

@ban_bp.route('/admin/fingerprint/<fingerprint>')
@require_admin
def admin_fingerprint_info(fingerprint):
    """Admin page to view detailed fingerprint information"""
    info = BanManager.get_fingerprint_info(fingerprint)
    return render_template('admin/fingerprint_info.html', info=info)

@ban_bp.route('/admin/ban-events')
@require_admin
def admin_ban_events():
    """Admin page to view recent ban events"""
    events = BanEventLogger.get_recent_events(100)
    return render_template('admin/ban_events.html', events=events)

# API routes for ban management
@ban_bp.route('/api/ban', methods=['POST'])
@require_admin
def api_ban_fingerprint():
    """API endpoint to ban a fingerprint"""
    data = request.get_json()
    if not data:
        return jsonify({'success': False, 'error': 'Invalid JSON data'}), 400
    
    fingerprint = data.get('fingerprint')
    reason = data.get('reason', 'Banned via API')
    admin_user = session.get('username', 'API Admin')
    
    if not fingerprint:
        return jsonify({'success': False, 'error': 'Fingerprint is required'}), 400
    
    success, message = BanManager.ban_fingerprint(fingerprint, reason, admin_user)
    
    return jsonify({
        'success': success,
        'message': message
    }), 200 if success else 400

@ban_bp.route('/api/unban', methods=['POST'])
@require_admin
def api_unban_fingerprint():
    """API endpoint to unban a fingerprint"""
    data = request.get_json()
    if not data:
        return jsonify({'success': False, 'error': 'Invalid JSON data'}), 400
    
    fingerprint = data.get('fingerprint')
    admin_user = session.get('username', 'API Admin')
    
    if not fingerprint:
        return jsonify({'success': False, 'error': 'Fingerprint is required'}), 400
    
    success, message = BanManager.unban_fingerprint(fingerprint, admin_user)
    
    return jsonify({
        'success': success,
        'message': message
    }), 200 if success else 400

@ban_bp.route('/api/check-ban/<fingerprint>')
def api_check_ban(fingerprint):
    """API endpoint to check if fingerprint is banned"""
    ban_status = BanManager.is_banned(fingerprint)
    return jsonify(ban_status)

# Middleware function to check bans
def check_fingerprint_ban():
    """
    Middleware function to check if current fingerprint is banned
    Should be called in before_request hooks
    """
    # Skip ban check for admin and ban management routes
    if request.endpoint and (request.endpoint.startswith('ban.') or
                           request.endpoint.startswith('admin.') or
                           request.endpoint.startswith('auth.')):
        return None

    # Skip ban check for search engine bots
    from app.utils.seo import BotDetector
    from app.utils.common import get_client_ip
    user_agent = request.headers.get('User-Agent', '')
    client_ip = get_client_ip()
    if BotDetector.should_bypass_security(user_agent, client_ip):
        return None

    # Get fingerprint from cookie or header
    fingerprint = request.cookies.get('fingerprint') or request.headers.get('X-Visitor-FP')

    if fingerprint:
        ban_status = BanManager.is_banned(fingerprint)
        if ban_status['banned']:
            # Don't log access attempts in middleware to avoid database locking
            # The ban check itself is sufficient for security
            abort(403, f"Access denied: {ban_status['reason']}")

    return None
