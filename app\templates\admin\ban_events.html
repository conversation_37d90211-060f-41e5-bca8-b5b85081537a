{% extends "base.html" %}

{% block title %}Security Events - PEPE Store Admin{% endblock %}

{% block content %}
<div class="container-fluid my-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-clock-history text-warning"></i> Security Events</h2>
                <div>
                    <a href="{{ url_for('ban.admin_ban_list') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> Back to Ban Management
                    </a>
                    <button class="btn btn-outline-info" onclick="refreshEvents()">
                        <i class="bi bi-arrow-clockwise"></i> Refresh
                    </button>
                </div>
            </div>

            {% if events %}
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Recent Security Events ({{ events|length }} shown)</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>Timestamp</th>
                                    <th>Event Type</th>
                                    <th>Fingerprint</th>
                                    <th>Reason/Details</th>
                                    <th>Admin User</th>
                                    <th>IP Address</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for event in events %}
                                <tr class="{% if event.event_type == 'banned' %}table-danger{% elif event.event_type == 'unbanned' %}table-success{% elif event.event_type == 'access_denied' %}table-warning{% endif %}">
                                    <td>
                                        <small>{{ event.created_at }}</small>
                                    </td>
                                    <td>
                                        {% if event.event_type == 'banned' %}
                                        <span class="badge bg-danger">
                                            <i class="bi bi-shield-x"></i> Banned
                                        </span>
                                        {% elif event.event_type == 'unbanned' %}
                                        <span class="badge bg-success">
                                            <i class="bi bi-shield-check"></i> Unbanned
                                        </span>
                                        {% elif event.event_type == 'access_denied' %}
                                        <span class="badge bg-warning">
                                            <i class="bi bi-exclamation-triangle"></i> Access Denied
                                        </span>
                                        {% else %}
                                        <span class="badge bg-info">
                                            <i class="bi bi-info-circle"></i> {{ event.event_type.title() }}
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <code class="text-muted">{{ event.fingerprint[:16] }}...</code>
                                        <button class="btn btn-sm btn-outline-secondary ms-1" 
                                                onclick="copyToClipboard('{{ event.fingerprint }}')" 
                                                title="Copy full fingerprint">
                                            <i class="bi bi-clipboard"></i>
                                        </button>
                                        <a href="{{ url_for('ban.admin_fingerprint_info', fingerprint=event.fingerprint) }}" 
                                           class="btn btn-sm btn-outline-info ms-1" title="View Details">
                                            <i class="bi bi-info-circle"></i>
                                        </a>
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ event.reason or 'No details' }}</span>
                                    </td>
                                    <td>
                                        {{ event.admin_user or 'System' }}
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ event.ip_address or 'N/A' }}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i> No security events recorded yet.
            </div>
            {% endif %}

            <!-- Event Statistics -->
            <div class="row mt-4">
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h4>{{ events|selectattr('event_type', 'equalto', 'banned')|list|length }}</h4>
                            <p class="mb-0">Bans</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h4>{{ events|selectattr('event_type', 'equalto', 'unbanned')|list|length }}</h4>
                            <p class="mb-0">Unbans</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-dark">
                        <div class="card-body text-center">
                            <h4>{{ events|selectattr('event_type', 'equalto', 'access_denied')|list|length }}</h4>
                            <p class="mb-0">Access Denied</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h4>{{ events|length }}</h4>
                            <p class="mb-0">Total Events</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Event Timeline -->
            {% if events %}
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Event Timeline</h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        {% for event in events[:10] %}
                        <div class="timeline-item mb-3">
                            <div class="d-flex">
                                <div class="flex-shrink-0">
                                    {% if event.event_type == 'banned' %}
                                    <i class="bi bi-shield-x text-danger fs-5"></i>
                                    {% elif event.event_type == 'unbanned' %}
                                    <i class="bi bi-shield-check text-success fs-5"></i>
                                    {% elif event.event_type == 'access_denied' %}
                                    <i class="bi bi-exclamation-triangle text-warning fs-5"></i>
                                    {% else %}
                                    <i class="bi bi-info-circle text-info fs-5"></i>
                                    {% endif %}
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="fw-bold">{{ event.event_type.title() }}</div>
                                    <div class="text-muted">
                                        Fingerprint: <code>{{ event.fingerprint[:16] }}...</code>
                                    </div>
                                    <div class="text-muted small">{{ event.reason }}</div>
                                    <div class="text-muted small">
                                        {% if event.admin_user %}by {{ event.admin_user }} • {% endif %}
                                        {{ event.created_at }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Refresh events
function refreshEvents() {
    location.reload();
}

// Copy to clipboard function
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        const btn = event.target.closest('button');
        const originalHTML = btn.innerHTML;
        btn.innerHTML = '<i class="bi bi-check text-success"></i>';
        setTimeout(() => {
            btn.innerHTML = originalHTML;
        }, 1000);
    });
}

// Auto-refresh every 30 seconds
setInterval(function() {
    // Add a subtle indicator that auto-refresh is happening
    const refreshBtn = document.querySelector('[onclick="refreshEvents()"]');
    if (refreshBtn) {
        const originalHTML = refreshBtn.innerHTML;
        refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> Auto-refresh';
        setTimeout(() => {
            location.reload();
        }, 1000);
    }
}, 30000);
</script>

<style>
.timeline-item {
    position: relative;
}

.timeline-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 12px;
    top: 30px;
    bottom: -15px;
    width: 2px;
    background-color: #dee2e6;
}

.spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
</style>
{% endblock %}
