"""
Flask Application Factory
"""
import os
import logging
from flask import Flask, request, abort, render_template_string


def create_app(config_name='default'):
    """Application factory pattern"""
    app = Flask(__name__, static_folder='static', static_url_path='/static')

    # Load configuration
    from app.config import Config
    app.config.from_object(Config)
    Config.init_app(app)

    # Initialize logging
    setup_logging(app)

    # Initialize database
    from app.models import init_database
    init_database()

    # Register blueprints
    register_blueprints(app)

    # Register template filters
    register_template_filters(app)

    # Register IP blocking middleware
    register_ip_blocking_middleware(app)

    # Initialize security middleware
    register_security_middleware(app)

    return app


def setup_logging(app):
    """Setup application logging"""
    log_folder = app.config.get('LOG_FOLDER', 'app/logs')
    log_file = app.config.get('LOG_FILE', 'app.log')
    security_log_file = app.config.get('SECURITY_LOG_FILE', 'security.log')

    log_path = os.path.join(log_folder, log_file)
    security_log_path = os.path.join(log_folder, security_log_file)

    # Ensure log directory exists
    os.makedirs(log_folder, exist_ok=True)

    # Setup main application logger
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s %(levelname)s: %(message)s',
        handlers=[
            logging.FileHandler(log_path),
            logging.StreamHandler()
        ]
    )

    # Setup security logger for authentication events
    security_logger = logging.getLogger('security')
    security_logger.setLevel(logging.INFO)

    # Clear any existing handlers to avoid duplicates
    security_logger.handlers.clear()

    # Create security log handler
    security_handler = logging.FileHandler(security_log_path)
    security_handler.setLevel(logging.INFO)

    # Create security log formatter
    security_formatter = logging.Formatter(
        '%(asctime)s [SECURITY] %(levelname)s: %(message)s'
    )
    security_handler.setFormatter(security_formatter)

    # Add handler to security logger
    security_logger.addHandler(security_handler)

    # Prevent propagation to root logger to avoid duplicate messages
    security_logger.propagate = False

    # Also log security events to console in debug mode
    if app.debug:
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(security_formatter)
        security_logger.addHandler(console_handler)

    # Store security logger in app config for easy access
    app.security_logger = security_logger


def register_blueprints(app):
    """Register all blueprints"""
    from app.blueprints.main import main_bp
    from app.blueprints.auth import auth_bp
    from app.blueprints.admin import admin_bp
    from app.blueprints.publisher import publisher_bp
    from app.blueprints.api import api_bp
    from app.fp import fp_bp
    from app.ban import ban_bp
    from app.referral import referral_bp
    from app.rating import rating_bp
    from app.security import security_bp

    app.register_blueprint(main_bp)
    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(admin_bp, url_prefix='/admin')
    app.register_blueprint(publisher_bp, url_prefix='/publisher')
    app.register_blueprint(api_bp, url_prefix='/api')
    app.register_blueprint(fp_bp, url_prefix='/fp')
    app.register_blueprint(ban_bp)
    app.register_blueprint(referral_bp)
    app.register_blueprint(rating_bp)
    app.register_blueprint(security_bp)


def register_template_filters(app):
    """Register custom template filters"""
    from app.utils.template_filters import register_filters
    register_filters(app)


def register_ip_blocking_middleware(app):
    """Register IP blocking middleware"""

    @app.before_request
    def check_blocked_ip():
        """Check if the requesting IP is blocked before processing any request"""
        from app.utils.common import get_client_ip
        from app.models import BlockedIP
        from app.config import Config
        from app.utils.seo import BotDetector

        # Skip IP blocking check if disabled
        if not Config.IP_BLOCK_ENABLED:
            return

        # Get client IP
        client_ip = get_client_ip()

        # Skip check for search engine bots
        user_agent = request.headers.get('User-Agent', '')
        if BotDetector.should_bypass_security(user_agent, client_ip):
            return

        # Skip check in development mode for local networks and test environments
        if Config.DEVELOPMENT_MODE:
            if (client_ip.startswith(('192.168.', '10.', '172.')) or
                client_ip == 'testclient' or
                not client_ip or
                client_ip.startswith('127.')):
                return

        # Check if IP is blocked
        if BlockedIP.is_blocked(client_ip):
            # Create blocked page HTML
            blocked_html = '''
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Access Blocked - PEPE Store</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Rubik:wght@400;500;700&display=swap" rel="stylesheet">
  <style>
    body {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      font-family: 'Rubik', sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 1rem;
    }

    .blocked-card {
      background: rgba(255, 255, 255, 0.85);
      backdrop-filter: blur(15px);
      border: none;
      border-radius: 1.5rem;
      box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
      overflow: hidden;
      transition: all 0.3s ease-in-out;
    }

    .blocked-icon {
      font-size: 4rem;
      color: #dc3545;
    }

    .blocked-title {
      color: #dc3545;
      font-weight: 700;
      font-size: 1.75rem;
    }

    .alert {
      font-size: 0.95rem;
      text-align: left;
    }

    .btn i {
      margin-right: 0.5rem;
    }

    .card-footer {
      background: transparent;
      border-top: none;
      padding: 1rem;
      font-size: 0.875rem;
    }

    @media (max-width: 576px) {
      .blocked-icon {
        font-size: 3rem;
      }

      .blocked-title {
        font-size: 1.5rem;
      }

      .card-body {
        padding: 2rem 1.5rem;
      }
    }
  </style>
</head>
<body>
  <div class="card blocked-card w-100" style="max-width: 500px;">
    <div class="card-body text-center">
      <i class="bi bi-shield-x blocked-icon mb-3"></i>
      <h1 class="blocked-title mb-2">Access Blocked</h1>
      <p class="lead mb-3">Your IP address has been temporarily blocked due to suspicious activity.</p>
      <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle"></i> <strong>IP Address: </strong>'''+client_ip+'''<br>
        <strong>Reason:</strong> Security violation detected
      </div>
      <p class="text-muted mb-4">If you believe this is an error, please contact the administrator.</p>
      <div class="d-grid gap-2">
        <a href="https://t.me/pepestoreapps" class="btn btn-primary" target="_blank">
          <i class="bi bi-telegram"></i> Contact Support
        </a>
        <button class="btn btn-outline-secondary" onclick="history.back()">
          <i class="bi bi-arrow-left"></i> Go Back
        </button>
      </div>
    </div>
    <div class="card-footer text-center text-muted">
      🐸 PEPE Store Security System
    </div>
  </div>
</body>
</html>
            '''

            # Return blocked page with 403 status
            return blocked_html, 403


def register_security_middleware(app):
    """Register security middleware"""
    from app.security import init_security_middleware
    init_security_middleware(app)
