import sqlite3
import os
import sys

def analyze_db(db_path):
    if not os.path.exists(db_path):
        print("Database file not found.")
        return

    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    print("=== DATABASE ANALYSIS ===\n")

    # File stats
    file_size = os.path.getsize(db_path)
    print(f"Database file: {db_path}")
    print(f"File size: {file_size / 1024 / 1024:.2f} MB")

    # Page size
    cursor.execute("PRAGMA page_size")
    page_size = cursor.fetchone()[0]
    print(f"Page size: {page_size} bytes")

    # Page count
    cursor.execute("PRAGMA page_count")
    page_count = cursor.fetchone()[0]
    print(f"Total pages: {page_count}")
    print(f"Total size (from pages): {page_size * page_count / 1024 / 1024:.2f} MB")

    # Free pages
    cursor.execute("PRAGMA freelist_count")
    free_pages = cursor.fetchone()[0]
    print(f"Free pages: {free_pages}")

    print("\n--- Table Row Counts ---")
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = [row[0] for row in cursor.fetchall()]
    for table in tables:
        try:
            cursor.execute(f"SELECT COUNT(*) FROM '{table}'")
            count = cursor.fetchone()[0]
            print(f"{table:<30} → {count} rows")
        except sqlite3.DatabaseError:
            print(f"{table:<30} → (could not read)")

    print("\n--- Indexes ---")
    cursor.execute("SELECT name, tbl_name FROM sqlite_master WHERE type='index'")
    indexes = cursor.fetchall()
    for name, tbl in indexes:
        print(f"{name:<30} on {tbl}")

    conn.close()

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python analyzedb.py yourfile.db")
    else:
        analyze_db(sys.argv[1])
