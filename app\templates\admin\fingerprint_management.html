{% extends "base.html" %}

{% block title %}Fingerprint Management - PEPE Store Admin{% endblock %}

{% block content %}
<div class="container-fluid my-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-fingerprint text-primary"></i> Fingerprint Management</h2>
                <div>
                    <button class="btn btn-outline-info" onclick="refreshData()">
                        <i class="bi bi-arrow-clockwise"></i> Refresh
                    </button>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#searchModal">
                        <i class="bi bi-search"></i> Search Fingerprint
                    </button>
                </div>
            </div>

            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h4>{{ stats.total_fingerprints }}</h4>
                            <p class="mb-0">Total Fingerprints</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h4>{{ stats.active_today }}</h4>
                            <p class="mb-0">Active Today</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-dark">
                        <div class="card-body text-center">
                            <h4>{{ stats.banned_count }}</h4>
                            <p class="mb-0">Banned</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h4>{{ stats.unique_ips }}</h4>
                            <p class="mb-0">Unique IPs</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Fingerprints -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Recent Fingerprints</h5>
                </div>
                <div class="card-body p-0">
                    {% if fingerprints %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>Fingerprint</th>
                                    <th>IP Address</th>
                                    <th>User Agent</th>
                                    <th>Visits</th>
                                    <th>First Seen</th>
                                    <th>Last Seen</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for fp in fingerprints %}
                                <tr class="{% if fp.is_banned %}table-danger{% endif %}">
                                    <td>
                                        <code class="text-primary">{{ fp.fingerprint[:16] }}...</code>
                                        <button class="btn btn-sm btn-outline-secondary ms-1" 
                                                onclick="copyToClipboard('{{ fp.fingerprint }}')" 
                                                title="Copy full fingerprint">
                                            <i class="bi bi-clipboard"></i>
                                        </button>
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ fp.ip_address or 'N/A' }}</span>
                                        {% if fp.ip_address %}
                                        <button class="btn btn-sm btn-outline-info ms-1" 
                                                onclick="searchByIP('{{ fp.ip_address }}')" 
                                                title="Find other fingerprints from this IP">
                                            <i class="bi bi-search"></i>
                                        </button>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted" title="{{ fp.user_agent }}">
                                            {{ (fp.user_agent[:50] + '...') if fp.user_agent and fp.user_agent|length > 50 else (fp.user_agent or 'N/A') }}
                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ fp.visit_count or 1 }}</span>
                                    </td>
                                    <td>
                                        <small>{{ fp.created_at }}</small>
                                    </td>
                                    <td>
                                        <small>{{ fp.last_seen or fp.created_at }}</small>
                                    </td>
                                    <td>
                                        {% if fp.is_banned %}
                                        <span class="badge bg-danger">BANNED</span>
                                        {% else %}
                                        <span class="badge bg-success">ACTIVE</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('ban.admin_fingerprint_info', fingerprint=fp.fingerprint) }}" 
                                               class="btn btn-outline-info" title="View Details">
                                                <i class="bi bi-info-circle"></i>
                                            </a>
                                            {% if not fp.is_banned %}
                                            <button class="btn btn-outline-danger" 
                                                    onclick="banFingerprint('{{ fp.fingerprint }}')" 
                                                    title="Ban Fingerprint">
                                                <i class="bi bi-shield-x"></i>
                                            </button>
                                            {% else %}
                                            <a href="{{ url_for('ban.admin_unban_fingerprint', fingerprint=fp.fingerprint) }}" 
                                               class="btn btn-outline-success" 
                                               onclick="return confirm('Unban this fingerprint?')" 
                                               title="Unban">
                                                <i class="bi bi-shield-check"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="p-4 text-center text-muted">
                        <i class="bi bi-fingerprint fs-1"></i><br>
                        No fingerprints found
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- IP Address Analysis -->
            <div class="row mt-4">
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Top IP Addresses</h5>
                        </div>
                        <div class="card-body">
                            {% if ip_stats %}
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>IP Address</th>
                                            <th>Fingerprints</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for ip_stat in ip_stats %}
                                        <tr>
                                            <td>{{ ip_stat.ip_address }}</td>
                                            <td>
                                                <span class="badge bg-{{ 'warning' if ip_stat.count > 5 else 'info' }}">
                                                    {{ ip_stat.count }}
                                                </span>
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary" 
                                                        onclick="searchByIP('{{ ip_stat.ip_address }}')">
                                                    <i class="bi bi-search"></i> View All
                                                </button>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <p class="text-muted">No IP statistics available</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Recent Activity</h5>
                        </div>
                        <div class="card-body">
                            {% if recent_activity %}
                            <div class="timeline">
                                {% for activity in recent_activity %}
                                <div class="timeline-item mb-3">
                                    <div class="d-flex">
                                        <div class="flex-shrink-0">
                                            <i class="bi bi-fingerprint text-primary"></i>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <div class="fw-bold">New Fingerprint</div>
                                            <div class="text-muted small">
                                                <code>{{ activity.fingerprint[:16] }}...</code>
                                            </div>
                                            <div class="text-muted small">
                                                IP: {{ activity.ip_address }} • {{ activity.created_at }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            {% else %}
                            <p class="text-muted">No recent activity</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search Modal -->
<div class="modal fade" id="searchModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Search Fingerprints</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="searchType" class="form-label">Search Type</label>
                    <select class="form-select" id="searchType">
                        <option value="fingerprint">Fingerprint Hash</option>
                        <option value="ip">IP Address</option>
                        <option value="user_agent">User Agent</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="searchValue" class="form-label">Search Value</label>
                    <input type="text" class="form-control" id="searchValue" 
                           placeholder="Enter search value">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="performSearch()">Search</button>
            </div>
        </div>
    </div>
</div>

<!-- Ban Modal -->
<div class="modal fade" id="banModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Ban Fingerprint</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="banForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="banReason" class="form-label">Reason</label>
                        <select class="form-select" id="banReason" required>
                            <option value="">Select reason...</option>
                            <option value="Abuse/Spam">Abuse/Spam</option>
                            <option value="Malicious Activity">Malicious Activity</option>
                            <option value="Terms Violation">Terms Violation</option>
                            <option value="Security Threat">Security Threat</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
                    <div class="mb-3" id="customReasonDiv" style="display: none;">
                        <label for="customBanReason" class="form-label">Custom Reason</label>
                        <input type="text" class="form-control" id="customBanReason" 
                               placeholder="Enter custom reason">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Ban Fingerprint</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Refresh data
function refreshData() {
    location.reload();
}

// Copy to clipboard
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        const btn = event.target.closest('button');
        const originalHTML = btn.innerHTML;
        btn.innerHTML = '<i class="bi bi-check text-success"></i>';
        setTimeout(() => {
            btn.innerHTML = originalHTML;
        }, 1000);
    });
}

// Search by IP
function searchByIP(ip) {
    document.getElementById('searchType').value = 'ip';
    document.getElementById('searchValue').value = ip;
    new bootstrap.Modal(document.getElementById('searchModal')).show();
}

// Perform search
function performSearch() {
    const type = document.getElementById('searchType').value;
    const value = document.getElementById('searchValue').value.trim();
    
    if (!value) {
        alert('Please enter a search value');
        return;
    }
    
    const params = new URLSearchParams();
    params.append('search_type', type);
    params.append('search_value', value);
    
    window.location.href = '{{ url_for("admin.fingerprint_management") }}?' + params.toString();
}

// Ban fingerprint
function banFingerprint(fingerprint) {
    document.getElementById('banForm').setAttribute('data-fingerprint', fingerprint);
    new bootstrap.Modal(document.getElementById('banModal')).show();
}

// Handle ban reason change
document.getElementById('banReason').addEventListener('change', function() {
    const customDiv = document.getElementById('customReasonDiv');
    if (this.value === 'Other') {
        customDiv.style.display = 'block';
        document.getElementById('customBanReason').required = true;
    } else {
        customDiv.style.display = 'none';
        document.getElementById('customBanReason').required = false;
    }
});

// Handle ban form submission
document.getElementById('banForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const fingerprint = this.getAttribute('data-fingerprint');
    let reason = document.getElementById('banReason').value;
    
    if (reason === 'Other') {
        reason = document.getElementById('customBanReason').value;
    }
    
    if (!reason) {
        alert('Please select or enter a reason');
        return;
    }
    
    try {
        const response = await fetch('/api/ban', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Visitor-FP': getCookie('fingerprint') || ''
            },
            body: JSON.stringify({
                fingerprint: fingerprint,
                reason: reason
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert('Fingerprint banned successfully');
            location.reload();
        } else {
            alert('Error: ' + result.message);
        }
    } catch (error) {
        alert('Network error: ' + error.message);
    }
});

// Get cookie helper
function getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
}
</script>
{% endblock %}
