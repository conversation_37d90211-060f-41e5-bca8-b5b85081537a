# token MTM5MzE2Nzc4MjQxMzMzNjY4Nw.Gygh1o.aJR5XrUDvgXgwyvVK38sZS8CoqUQ8xInijl9o8
import discord
from discord import app_commands
from discord.ext import commands
from discord.utils import get
import asyncio
import sqlite3
import datetime


intents = discord.Intents.all()
bot = commands.Bot(command_prefix='!', intents=intents)
tree = bot.tree

# === SQLite DB Setup ===
conn = sqlite3.connect('bot_data.db')
c = conn.cursor()
c.execute('''CREATE TABLE IF NOT EXISTS tickets (user_id INTEGER PRIMARY KEY, channel_id INTEGER)''')
c.execute('''CREATE TABLE IF NOT EXISTS verified_users (user_id INTEGER PRIMARY KEY)''')
c.execute('''
CREATE TABLE IF NOT EXISTS user_stats (
    user_id INTEGER PRIMARY KEY,
    messages INTEGER DEFAULT 0,
    warnings INTEGER DEFAULT 0,
    invites INTEGER DEFAULT 0,
    join_date TEXT,
    leave_date TEXT
)
''')
c.execute('''CREATE TABLE IF NOT EXISTS warnings (user_id INTEGER PRIMARY KEY, count INTEGER DEFAULT 0)''')
c.execute('''CREATE TABLE IF NOT EXISTS blacklist (user_id INTEGER PRIMARY KEY)''')
c.execute('''CREATE TABLE IF NOT EXISTS messages (user_id INTEGER PRIMARY KEY, count INTEGER DEFAULT 0)''')
c.execute('''CREATE TABLE IF NOT EXISTS invites (inviter_id INTEGER PRIMARY KEY, count INTEGER DEFAULT 0)''')

conn.commit()

WELCOME_CHANNEL_ID = 1393165993722908717
LOG_CHANNEL_ID = 1393165994410901597
TICKET_CATEGORY_ID = 1393170997372518482
SUPPORT_ROLE_NAME = "Support"
ALLOWED_GUILD_ID = 1393165992288452799
invites = {}

def is_not_blacklisted():
    async def predicate(interaction: discord.Interaction):
        if interaction.user.guild_permissions.administrator:
            return True
        result = c.execute("SELECT 1 FROM blacklist WHERE user_id = ?", (interaction.user.id,)).fetchone()
        if result:
            await interaction.response.send_message("⛔ You are blacklisted and cannot use this command.", ephemeral=True)
            return False
        return True
    return app_commands.check(predicate)


@bot.event
async def on_ready():
    guild = discord.Object(id=ALLOWED_GUILD_ID)
    await tree.sync(guild=discord.Object(id=ALLOWED_GUILD_ID))
    print(f"Synced commands to guild {ALLOWED_GUILD_ID}")
    await bot.change_presence(activity=discord.Activity(type=discord.ActivityType.watching, name="pepe store 👀"))
    print(f"✅ Logged in as {bot.user}")

@bot.event
async def on_member_join(member):
    # Check if user is blacklisted in the database
    result = c.execute("SELECT 1 FROM blacklist WHERE user_id = ?", (member.id,)).fetchone()
    
    if result:
        # Remove all roles except @everyone
        roles_to_remove = [role for role in member.roles if role.name != "@everyone"]
        try:
            await member.remove_roles(*roles_to_remove, reason="Blacklisted user rejoined")
        except discord.Forbidden:
            print(f"⚠️ Unable to remove roles from {member}. Missing permissions.")
        
        # Assign the Blacklisted role (create if missing)
        blacklisted_role = discord.utils.get(member.guild.roles, name="Blacklisted")
        if not blacklisted_role:
            blacklisted_role = await member.guild.create_role(
                name="Blacklisted", color=discord.Color.dark_gray(), reason="Blacklist role created"
            )
        
        await member.add_roles(blacklisted_role, reason="Rejoined as blacklisted")

        # Optional: log or notify in welcome channel
        channel = bot.get_channel(WELCOME_CHANNEL_ID)
        await channel.send(f"🚫 {member.mention} is blacklisted and has been restricted.")
        return
    
    # Normal welcome
    channel = bot.get_channel(WELCOME_CHANNEL_ID)
    await channel.send(f"🎉 Welcome {member.mention} to the server!")

@bot.event
async def on_member_remove(member):
    channel = bot.get_channel(WELCOME_CHANNEL_ID)
    await channel.send(f"👋 {member.name} has left the server.")

@bot.event
async def on_message_delete(message):
    if message.author.bot: return
    log = bot.get_channel(LOG_CHANNEL_ID)
    await log.send(f"🗑️ Message deleted in {message.channel.mention} by {message.author}: {message.content}")

@tree.command(name="changelog", description="Post a changelog (admin only)", guild=discord.Object(id=ALLOWED_GUILD_ID))
@app_commands.checks.has_permissions(administrator=True)
async def changelog(interaction: discord.Interaction, title: str, message: str):
    embed = discord.Embed(title=f"📢 {title}", description=message, color=0x00ff00)
    embed.timestamp = datetime.datetime.utcnow()
    await interaction.response.send_message(embed=embed)

@tree.command(name="ticket", description="Create a support ticket", guild=discord.Object(id=ALLOWED_GUILD_ID))
async def ticket(interaction: discord.Interaction):
    c.execute("SELECT channel_id FROM tickets WHERE user_id = ?", (interaction.user.id,))
    if c.fetchone():
        await interaction.response.send_message("❗ You already have an open ticket.", ephemeral=True)
        return

    category = bot.get_channel(TICKET_CATEGORY_ID)
    overwrites = {
        interaction.guild.default_role: discord.PermissionOverwrite(read_messages=False),
        interaction.user: discord.PermissionOverwrite(read_messages=True, send_messages=True),
        get(interaction.guild.roles, name=SUPPORT_ROLE_NAME): discord.PermissionOverwrite(read_messages=True)
    }
    channel = await interaction.guild.create_text_channel(f"ticket-{interaction.user.name}", overwrites=overwrites, category=category)
    c.execute("INSERT INTO tickets (user_id, channel_id) VALUES (?, ?)", (interaction.user.id, channel.id))
    conn.commit()
    await channel.send(f"🎟️ {interaction.user.mention}, a staff member will be with you shortly.")
    await interaction.response.send_message(f"✅ Ticket created: {channel.mention}", ephemeral=True)

@tree.command(name="close", description="Close your support ticket", guild=discord.Object(id=ALLOWED_GUILD_ID))
async def close(interaction: discord.Interaction):
    c.execute("SELECT user_id FROM tickets WHERE channel_id = ?", (interaction.channel.id,))
    result = c.fetchone()
    if result:
        await interaction.response.send_message("🔒 Closing ticket...")
        await asyncio.sleep(2)
        await interaction.channel.delete()
        c.execute("DELETE FROM tickets WHERE channel_id = ?", (interaction.channel.id,))
        conn.commit()

@tree.command(name="ban", description="Ban a member (admin only)", guild=discord.Object(id=ALLOWED_GUILD_ID))
@app_commands.checks.has_permissions(ban_members=True)
async def ban(interaction: discord.Interaction, member: discord.Member, reason: str = "No reason provided"):
    await member.ban(reason=reason)
    await interaction.response.send_message(f"🔨 {member} has been banned.")

@tree.command(name="kick", description="Kick a member (admin only)", guild=discord.Object(id=ALLOWED_GUILD_ID))
@app_commands.checks.has_permissions(kick_members=True)
async def kick(interaction: discord.Interaction, member: discord.Member, reason: str = "No reason provided"):
    await member.kick(reason=reason)
    await interaction.response.send_message(f"👢 {member} has been kicked.")

@tree.command(name="purge", description="Delete messages in a channel (admin only)", guild=discord.Object(id=ALLOWED_GUILD_ID))
@app_commands.checks.has_permissions(manage_messages=True)
async def purge(interaction: discord.Interaction, amount: int):
    await interaction.channel.purge(limit=amount + 1)
    await interaction.response.send_message(f"🧹 Deleted {amount} messages.", ephemeral=True)

@tree.command(name="warn", description="Warn a user", guild=discord.Object(id=ALLOWED_GUILD_ID))
@app_commands.checks.has_permissions(manage_messages=True)
async def warn(interaction: discord.Interaction, member: discord.Member, reason: str):
    c.execute("INSERT OR IGNORE INTO warnings (user_id) VALUES (?)", (member.id,))
    c.execute("UPDATE warnings SET count = count + 1 WHERE user_id = ?", (member.id,))
    conn.commit()

    c.execute("SELECT count FROM warnings WHERE user_id = ?", (member.id,))
    count = c.fetchone()[0]

    embed = discord.Embed(title="⚠️ User Warned", description=f"**{member.mention}** warned for: {reason}\nTotal warnings: {count}", color=0xFFFF00)
    await interaction.response.send_message(embed=embed)

    if count >= 4:
        await member.ban(reason="Reached 4 warnings")
        await interaction.followup.send(f"🔨 {member.mention} has been auto-banned for reaching 4 warnings.")

@tree.command(name="unwarn", description="Remove a warning from a user", guild=discord.Object(id=ALLOWED_GUILD_ID))
@app_commands.checks.has_permissions(manage_messages=True)
async def unwarn(interaction: discord.Interaction, member: discord.Member):
    c.execute("SELECT count FROM warnings WHERE user_id = ?", (member.id,))
    result = c.fetchone()
    if result and result[0] > 0:
        c.execute("UPDATE warnings SET count = count - 1 WHERE user_id = ?", (member.id,))
        conn.commit()
        await interaction.response.send_message(f"✅ Removed a warning from {member.mention}. Total warnings: {result[0] - 1}")
    else:
        await interaction.response.send_message(f"ℹ️ {member.mention} has no warnings.")

@tree.command(name="warnings", description="Check how many warnings a user has", guild=discord.Object(id=ALLOWED_GUILD_ID))
async def warnings(interaction: discord.Interaction, member: discord.Member):
    c.execute("SELECT count FROM warnings WHERE user_id = ?", (member.id,))
    result = c.fetchone()
    count = result[0] if result else 0
    await interaction.response.send_message(f"⚠️ {member.mention} has {count} warning(s).")

@tree.command(name="faq", description="Show frequently asked questions", guild=discord.Object(id=ALLOWED_GUILD_ID))
async def faq(interaction: discord.Interaction):
    embed = discord.Embed(
        title="📘 Frequently Asked Questions (FAQ)",
        description="Here are answers to the most commonly asked questions in this server.",
        color=0x00ff00
    )

    embed.add_field(name="❓ Q: How do I open a support ticket?", value="A: Use the `/ticket` command. A staff member will respond shortly.", inline=False)
    embed.add_field(name="🔒 Q: Are the apps shared in this server safe?", value="A: Yes. All apps are checked and scanned. If you're unsure, ask staff before using.", inline=False)
    embed.add_field(name="🚫 Q: Why was my message deleted?", value="A: Likely because it violated a server rule. Please review the `/rules` command.", inline=False)
    embed.add_field(name="📩 Q: Can I DM staff for help?", value="A: Please use the `/ticket` system. DMs to staff without permission may be ignored.", inline=False)
    embed.add_field(name="🔔 Q: How do I get notified about updates?", value="A: Turn on notifications from the <#1393957633488195665>", inline=False)
    embed.add_field(
        name="📨 Want to apply for staff or partner?",
        value="Please contact us on [Telegram](https://pepestore.xyz/s/telegram) or open a `/ticket` here in the server.",
        inline=False
    )

    embed.add_field(
        name="🌐 Official Accounts",
        value="[Telegram Channel](https://pepestore.xyz/s/telegram)\n[Discord Server](https://pepestore.xyz/s/discord)",
        inline=False
    )

    embed.set_footer(text="Still have questions? Use /ticket to contact support.")

    await interaction.response.send_message(embed=embed, ephemeral=True)

@tree.command(name="rules", description="Show server rules", guild=discord.Object(id=ALLOWED_GUILD_ID))
async def rules(interaction: discord.Interaction):
    embed = discord.Embed(title="📜 Server Rules", color=0xE67E22)

    embed.add_field(name="1. Be Respectful", value="Treat everyone with kindness and respect. Harassment, discrimination, bullying, or hate speech of any kind will result in an immediate ban.", inline=False)

    embed.add_field(name="2. No NSFW/NSFL Content", value="Absolutely no sexually explicit, violent, gory, or disturbing content. This includes images, text, links, or usernames. Violators will be permanently banned.", inline=False)

    embed.add_field(name="3. No Spam or Flooding", value="Do not spam messages, emojis, reactions, or commands. This includes mic spamming, repeated texts, or abusing bot commands.", inline=False)

    embed.add_field(name="4. No Advertising or Self-Promotion", value="This includes promoting your own or others' content, servers, links, or invites without prior staff approval. This rule applies in DMs too.", inline=False)

    embed.add_field(name="5. No Harmful or Illegal Activity", value="No discussion, promotion, or engagement in anything illegal or dangerous, including but not limited to: hacking, DDoS, drug use, or malware distribution.", inline=False)

    embed.add_field(name="6. Do Not Impersonate", value="Do not impersonate other users, staff, bots, or public figures. This includes using similar names, avatars, or pretending to speak for others.", inline=False)

    embed.add_field(name="7. Keep Usernames and Avatars Appropriate", value="Usernames, nicknames, and profile pictures must not contain offensive, misleading, or rule-breaking content.", inline=False)

    embed.add_field(name="8. Follow Discord's Terms of Service", value="You must follow all [Discord ToS](https://discord.com/terms) and [Community Guidelines](https://discord.com/guidelines). We will report violations.", inline=False)

    embed.add_field(name="9. Respect Staff and Decisions", value="Staff decisions are final. Arguing, backtalk, or trying to game the system may lead to punishment. Use the ticket system if you need to appeal.", inline=False)

    embed.add_field(name="10. English Only (unless permitted)", value="To ensure moderation and understanding, use English in public channels unless otherwise specified by staff.", inline=False)

    embed.add_field(name="11. No AI-Generated or Deepfake Content", value="Do not share AI-generated media, deepfakes, or altered content that may be misleading, malicious, or offensive.", inline=False)

    embed.add_field(name="12. No Bypassing Filters or Rules", value="Trying to bypass word filters, bans, or rules in any way will result in harsher punishment.", inline=False)

    embed.add_field(name="13. No Mass Mentions or Pings", value="Do not use @everyone, @here, or mass ping users without permission. This will result in mute or ban.", inline=False)

    embed.add_field(name="14. No Unapproved Bots or Scripts", value="Do not use unauthorized bots, self-bots, or scripts. All integrations must be approved by staff.", inline=False)

    embed.add_field(name="15. No Personal Information", value="Do not share your or others' personal information (doxing). This includes real names, addresses, phone numbers, etc.", inline=False)

    embed.set_footer(text="Violating any of these rules may result in a mute, kick, or permanent ban depending on the severity. Stay safe and enjoy the server!")

    await interaction.response.send_message(embed=embed, ephemeral=True)

@tree.command(name="timeout", description="Timeout a user for a given duration in seconds", guild=discord.Object(id=ALLOWED_GUILD_ID))
@app_commands.checks.has_permissions(moderate_members=True)
async def timeout(interaction: discord.Interaction, member: discord.Member, duration: int, reason: str = "No reason"):
    until = discord.utils.utcnow() + datetime.timedelta(seconds=duration)
    await member.timeout(until, reason=reason)
    await interaction.response.send_message(f"⏳ {member.mention} has been timed out for {duration} seconds.")

@tree.command(name="blacklist", description="Blacklist a user (admin only)", guild=discord.Object(id=ALLOWED_GUILD_ID))
@app_commands.checks.has_permissions(administrator=True)
async def blacklist(interaction: discord.Interaction, member: discord.Member):
    c.execute("INSERT OR IGNORE INTO blacklist (user_id) VALUES (?)", (member.id,))
    conn.commit()

    # Remove all roles except @everyone
    roles_to_remove = [role for role in member.roles if role.name != "@everyone"]
    try:
        await member.remove_roles(*roles_to_remove, reason="Blacklisted")
    except discord.Forbidden:
        await interaction.response.send_message("❌ I don't have permission to modify roles.", ephemeral=True)
        return

    # Give Blacklisted role
    blacklisted_role = discord.utils.get(interaction.guild.roles, name="Blacklisted")
    if not blacklisted_role:
        blacklisted_role = await interaction.guild.create_role(
            name="Blacklisted", color=discord.Color.dark_gray(), reason="Blacklist role created"
        )
    await member.add_roles(blacklisted_role, reason="User blacklisted")

    await interaction.response.send_message(f"🚫 {member.mention} has been blacklisted.")


@tree.command(name="unblacklist", description="Remove user from blacklist (admin only)", guild=discord.Object(id=ALLOWED_GUILD_ID))
@app_commands.checks.has_permissions(administrator=True)
async def unblacklist(interaction: discord.Interaction, member: discord.Member):
    c.execute("DELETE FROM blacklist WHERE user_id = ?", (member.id,))
    conn.commit()

    # Remove Blacklisted role
    blacklisted_role = discord.utils.get(interaction.guild.roles, name="Blacklisted")
    if blacklisted_role in member.roles:
        await member.remove_roles(blacklisted_role, reason="Unblacklisted")

    # Optional: restore a default role
    default_role = discord.utils.get(interaction.guild.roles, name="Member")
    if default_role:
        await member.add_roles(default_role, reason="Restoring default role")

    await interaction.response.send_message(f"✅ {member.mention} has been removed from the blacklist.")


# @tree.command(name="help", description="Show  helpmenu", guild=discord.Object(id=ALLOWED_GUILD_ID))
# async def help_command(interaction: discord.Interaction):
#     embed = discord.Embed(title="🤖 Bot Help Menu", color=0x3498db)
#     embed.add_field(name="/ticket", value="Open a support ticket", inline=False)
#     embed.add_field(name="/close", value="Close your ticket", inline=False)
#     embed.add_field(name="/changelog", value="Post a changelog (admin)", inline=False)
#     embed.add_field(name="/ban", value="Ban a member (admin)", inline=False)
#     embed.add_field(name="/kick", value="Kick a member (admin)", inline=False)
#     embed.add_field(name="/purge", value="Purge messages (admin)", inline=False)
#     embed.add_field(name="/warn", value="Warn a member", inline=False)
#     embed.add_field(name="/faq", value="Show FAQ", inline=False)
#     embed.add_field(name="/rules", value="Show server rules", inline=False)
#     embed.add_field(name="/timeout", value="Timeout a user", inline=False)
#     embed.add_field(name="/blacklist", value="Blacklist a user (admin)", inline=False)
#     embed.add_field(name="/unblacklist", value="Unblacklist a user (admin)", inline=False)
#     embed.add_field(name="/userinfo", value="Show user info (admin)", inline=False)
#     embed.add_field(name="/invites", value="Check your or others' invite count", inline=False)
#     await interaction.response.send_message(embed=embed, ephemeral=True)

@tree.command(name="userinfo", description="Show detailed user info", guild=discord.Object(id=ALLOWED_GUILD_ID))
@app_commands.checks.has_permissions(administrator=True)
async def userinfo(interaction: discord.Interaction, member: discord.Member):
    joined_at = member.joined_at.strftime("%Y-%m-%d %H:%M:%S") if member.joined_at else "Unknown"
    created_at = member.created_at.strftime("%Y-%m-%d %H:%M:%S")
    roles = ', '.join([r.name for r in member.roles if r.name != "@everyone"])
    c.execute("SELECT count FROM warnings WHERE user_id = ?", (member.id,))
    warns = c.fetchone()
    c.execute("SELECT count FROM messages WHERE user_id = ?", (member.id,))
    messages = c.fetchone() or (0,)
    
    c.execute("SELECT count FROM invites WHERE inviter_id = ?", (member.id,))
    invites = c.fetchone() or (0,)

    embed = discord.Embed(title=f"User Info: {member.name}", color=0x2ecc71)
    embed.set_thumbnail(url=member.display_avatar.url)
    embed.add_field(name="Username", value=member.mention, inline=True)
    embed.add_field(name="Warnings", value=warns[0] if warns else 0, inline=True)
    embed.add_field(name="Messages Sent", value=messages[0] if messages else 0, inline=True)
    embed.add_field(name="Invites", value=invites[0] if invites else 0, inline=True)
    embed.add_field(name="Joined At", value=joined_at, inline=False)
    embed.add_field(name="Account Created", value=created_at, inline=False)
    embed.add_field(name="Roles", value=roles or "None", inline=False)
    await interaction.response.send_message(embed=embed, ephemeral=True)

@tree.command(name="invites", description="Check invite count", guild=discord.Object(id=ALLOWED_GUILD_ID))
async def invites_command(interaction: discord.Interaction, member: discord.Member = None):
    target = member or interaction.user
    if member and not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("❌ Only admins can check other users' invites.", ephemeral=True)
        return
    c.execute("SELECT count FROM invites WHERE inviter_id = ?", (target.id,))
    count = c.fetchone() or (0,)

    embed = discord.Embed(title=f"📨 Invite Info", description=f"**{target.name}** has invited **{count[0] if count else 0}** members.", color=0x1abc9c)
    await interaction.response.send_message(embed=embed, ephemeral=True)




@tree.command(name="embed", description="Send a custom embed message", guild=discord.Object(id=ALLOWED_GUILD_ID))
@app_commands.describe(
    title="Title of the embed",
    description="Main content of the embed",
    footer="Footer text of the embed (optional)",
    color="Hex color (e.g., 0x3498db). Default is green.",
    channel="Channel to send the embed (optional)"
)
@app_commands.checks.has_permissions(administrator=True)
async def embed(
    interaction,  # no type hint here
    title: str,
    description: str,
    footer: str = None,
    color: str = "0x00ff00",
    channel: discord.TextChannel = None,
):
    # No need for manual permission check because of decorator

    try:
        color_value = int(color, 16)
    except ValueError:
        await interaction.response.send_message(
            "❌ Invalid color format. Use hex like `0x00ff00`.",
            ephemeral=True,
        )
        return

    embed_msg = discord.Embed(title=title, description=description, color=color_value)
    if footer:
        embed_msg.set_footer(text=footer)

    target_channel = channel or interaction.channel

    await interaction.response.defer(ephemeral=True)
    await target_channel.send(embed=embed_msg)
    await interaction.followup.send(
        f"✅ Embed sent to {target_channel.mention}", ephemeral=True
    )
    
@bot.event
async def on_message(message):
    if message.author.bot:
        return

    # Message count tracking
    c.execute("INSERT OR IGNORE INTO messages (user_id, count) VALUES (?, 0)", (message.author.id,))
    c.execute("UPDATE messages SET count = count + 1 WHERE user_id = ?", (message.author.id,))
    conn.commit()

    # Enhanced NSFW detection
    import re
    nsfw_patterns = [
        r'\b(porn|nude|hentai|xxx|sex|nsfw|onlyfans|blowjob|boobs|anal|cum|cock|dick|fuck|pussy)\b',
        r'(?i)(https?:\/\/)?(www\.)?(pornhub|xvideos|xhamster|redtube|onlyfans)\.com'
    ]

    if any(re.search(pattern, message.content.lower()) for pattern in nsfw_patterns):
        await message.delete()

        # Auto-warn user
        c.execute("INSERT OR IGNORE INTO warnings (user_id) VALUES (?)", (message.author.id,))
        c.execute("UPDATE warnings SET count = count + 1 WHERE user_id = ?", (message.author.id,))
        conn.commit()

        c.execute("SELECT count FROM warnings WHERE user_id = ?", (message.author.id,))
        count = c.fetchone()[0]

        warn_msg = await message.channel.send(
            f"🚫 NSFW content is not allowed, {message.author.mention}! This is warning #{count}."
        )
        await asyncio.sleep(5)
        await warn_msg.delete()

        # Auto-ban on 4 warnings
        if count >= 4:
            await message.author.ban(reason="Auto-banned for NSFW abuse (4 warnings)")
            log = bot.get_channel(LOG_CHANNEL_ID)
            await log.send(f"🔨 {message.author} auto-banned for reaching 4 warnings (NSFW).")
        return

    await bot.process_commands(message)


bot.run("MTM5MzE2Nzc4MjQxMzMzNjY4Nw.Gygh1o.aJR5XrUDvgXgwyvVK38sZS8CoqUQ8xInijl9o8")
