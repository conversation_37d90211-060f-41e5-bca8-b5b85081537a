{% extends "base.html" %}

{% block title %}PEPE Store - BOT CHECK{% endblock %}

{% block content %}
<div class="container-fluid d-flex align-items-center justify-content-center min-vh-100">
    <div class="text-center">
        <div class="mb-4">
            <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
        <h2 class="mb-3">🤖 Anti-bot check in progress...</h2>
        <p class="text-muted">Please wait while we verify you're not a robot.</p>
        <p class="text-muted small">You'll be redirected back to your original page after verification.</p>
        <div class="progress mt-3" style="height: 6px;">
            <div class="progress-bar progress-bar-striped progress-bar-animated"
                 role="progressbar" style="width: 0%" id="progressBar"></div>
        </div>
    </div>
</div>

<script>
    // Pass the next URL to the client script
    window.NEXT_URL = {{ next_url | tojson }};

    // Animate progress bar
    let progress = 0;
    const progressBar = document.getElementById('progressBar');
    const progressInterval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 90) progress = 90;
        progressBar.style.width = progress + '%';
    }, 200);

    // Stop progress animation when fingerprinting is complete
    window.stopProgress = () => {
        clearInterval(progressInterval);
        progressBar.style.width = '100%';
        progressBar.classList.remove('progress-bar-animated');
    };
</script>
<script src="{{ url_for('static', filename='js/client.js') }}"></script>
<script src="https://cdn.jsdelivr.net/npm/@thumbmarkjs/thumbmarkjs@rc/dist/thumbmark.umd.js'"></script>
{% endblock %}