{% extends "base.html" %}

{% block title %}Fingerprint Details - PEPE Store Admin{% endblock %}

{% block content %}
<div class="container-fluid my-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-fingerprint text-primary"></i> Fingerprint Details</h2>
                <div>
                    <a href="{{ url_for('ban.admin_ban_list') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> Back to Ban List
                    </a>
                    {% if not info.ban_status.banned %}
                    <button class="btn btn-danger" onclick="banFingerprint('{{ info.fingerprint }}')">
                        <i class="bi bi-shield-x"></i> Ban This Fingerprint
                    </button>
                    {% else %}
                    <a href="{{ url_for('ban.admin_unban_fingerprint', fingerprint=info.fingerprint) }}" 
                       class="btn btn-success" onclick="return confirm('Unban this fingerprint?')">
                        <i class="bi bi-shield-check"></i> Unban Fingerprint
                    </a>
                    {% endif %}
                </div>
            </div>

            <div class="row">
                <!-- Fingerprint Information -->
                <div class="col-lg-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Fingerprint Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Fingerprint Hash:</strong><br>
                                    <code class="text-primary">{{ info.fingerprint }}</code>
                                    <button class="btn btn-sm btn-outline-secondary ms-2" 
                                            onclick="copyToClipboard('{{ info.fingerprint }}')" 
                                            title="Copy fingerprint">
                                        <i class="bi bi-clipboard"></i>
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <strong>Status:</strong><br>
                                    {% if info.ban_status.banned %}
                                    <span class="badge bg-danger">BANNED</span>
                                    {% else %}
                                    <span class="badge bg-success">ACTIVE</span>
                                    {% endif %}
                                </div>
                            </div>
                            
                            {% if info.exists %}
                            <hr>
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>UUID:</strong><br>
                                    <code>{{ info.data.uuid or 'N/A' }}</code>
                                </div>
                                <div class="col-md-6">
                                    <strong>Visit Count:</strong><br>
                                    {{ info.data.visit_count or 0 }}
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <strong>First Seen:</strong><br>
                                    {{ info.data.created_at or 'N/A' }}
                                </div>
                                <div class="col-md-6">
                                    <strong>Last Seen:</strong><br>
                                    {{ info.data.last_seen or 'N/A' }}
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <strong>IP Address:</strong><br>
                                    {{ info.data.ip_address or 'N/A' }}
                                </div>
                                <div class="col-md-6">
                                    <strong>User Agent:</strong><br>
                                    <small class="text-muted">{{ info.data.user_agent or 'N/A' }}</small>
                                </div>
                            </div>
                            {% else %}
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle"></i> 
                                This fingerprint is not found in the database.
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Ban Information -->
                    {% if info.ban_status.banned %}
                    <div class="card mb-4">
                        <div class="card-header bg-danger text-white">
                            <h5 class="mb-0">Ban Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Reason:</strong><br>
                                    {{ info.ban_status.reason }}
                                </div>
                                <div class="col-md-6">
                                    <strong>Banned At:</strong><br>
                                    {{ info.ban_status.banned_at }}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- Activity Log -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Recent Events</h5>
                        </div>
                        <div class="card-body">
                            {% if info.events %}
                            <div class="timeline">
                                {% for event in info.events %}
                                <div class="timeline-item mb-3">
                                    <div class="d-flex">
                                        <div class="flex-shrink-0">
                                            {% if event.type == 'banned' %}
                                            <i class="bi bi-shield-x text-danger"></i>
                                            {% elif event.type == 'unbanned' %}
                                            <i class="bi bi-shield-check text-success"></i>
                                            {% elif event.type == 'access_denied' %}
                                            <i class="bi bi-exclamation-triangle text-warning"></i>
                                            {% else %}
                                            <i class="bi bi-info-circle text-info"></i>
                                            {% endif %}
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <div class="fw-bold">{{ event.type.title() }}</div>
                                            <div class="text-muted small">{{ event.reason }}</div>
                                            <div class="text-muted small">
                                                {% if event.admin %}by {{ event.admin }} • {% endif %}
                                                {{ event.timestamp }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            {% else %}
                            <div class="text-muted text-center">
                                <i class="bi bi-clock"></i><br>
                                No events recorded
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Ban Modal -->
<div class="modal fade" id="banModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Ban Fingerprint</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="banForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="reason" class="form-label">Reason</label>
                        <select class="form-select" id="reason" name="reason" required>
                            <option value="">Select reason...</option>
                            <option value="Abuse/Spam">Abuse/Spam</option>
                            <option value="Malicious Activity">Malicious Activity</option>
                            <option value="Terms Violation">Terms Violation</option>
                            <option value="Security Threat">Security Threat</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
                    <div class="mb-3" id="customReasonDiv" style="display: none;">
                        <label for="customReason" class="form-label">Custom Reason</label>
                        <input type="text" class="form-control" id="customReason" name="customReason" 
                               placeholder="Enter custom reason">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Ban Fingerprint</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Show/hide custom reason field
document.getElementById('reason').addEventListener('change', function() {
    const customDiv = document.getElementById('customReasonDiv');
    if (this.value === 'Other') {
        customDiv.style.display = 'block';
        document.getElementById('customReason').required = true;
    } else {
        customDiv.style.display = 'none';
        document.getElementById('customReason').required = false;
    }
});

// Ban fingerprint function
function banFingerprint(fingerprint) {
    document.getElementById('banForm').setAttribute('data-fingerprint', fingerprint);
    new bootstrap.Modal(document.getElementById('banModal')).show();
}

// Handle ban form submission
document.getElementById('banForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const fingerprint = this.getAttribute('data-fingerprint');
    let reason = formData.get('reason');
    
    if (reason === 'Other') {
        reason = formData.get('customReason');
    }
    
    if (!reason) {
        alert('Please select or enter a reason');
        return;
    }
    
    try {
        const response = await fetch('/api/ban', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Visitor-FP': getCookie('fingerprint') || ''
            },
            body: JSON.stringify({
                fingerprint: fingerprint,
                reason: reason
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert('Fingerprint banned successfully');
            location.reload();
        } else {
            alert('Error: ' + result.message);
        }
    } catch (error) {
        alert('Network error: ' + error.message);
    }
});

// Copy to clipboard function
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        const btn = event.target.closest('button');
        const originalHTML = btn.innerHTML;
        btn.innerHTML = '<i class="bi bi-check text-success"></i>';
        setTimeout(() => {
            btn.innerHTML = originalHTML;
        }, 1000);
    });
}

// Get cookie helper
function getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
}
</script>
{% endblock %}
