{% extends "base.html" %}

{% block title %}{{ stats.publisher_name }} - Referral Statistics{% endblock %}

{% block content %}
<div class="container my-4">
    <div class="row">
        <div class="col-12">
            <div class="text-center mb-4">
                <h2><i class="bi bi-graph-up text-success"></i> Referral Statistics</h2>
                <h4 class="text-muted">{{ stats.publisher_name }}</h4>
                <p class="text-muted">Code: <code class="text-primary">{{ stats.code }}</code></p>
            </div>

            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-people fs-1 mb-3"></i>
                            <h3 class="mb-0">{{ stats.total_referrals }}</h3>
                            <p class="mb-0">Total Unique Referrals</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-calendar-check fs-1 mb-3"></i>
                            <h3 class="mb-0">{{ stats.daily_stats|length }}</h3>
                            <p class="mb-0">Active Days (Last 30)</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-graph-up fs-1 mb-3"></i>
                            <h3 class="mb-0">
                                {% if stats.daily_stats %}
                                {{ (stats.daily_stats|sum(attribute='count') / stats.daily_stats|length)|round(1) }}
                                {% else %}
                                0
                                {% endif %}
                            </h3>
                            <p class="mb-0">Avg. Daily Referrals</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Daily Activity Chart -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Daily Activity (Last 30 Days)</h5>
                        </div>
                        <div class="card-body">
                            {% if stats.daily_stats %}
                            <canvas id="activityChart" width="400" height="200"></canvas>
                            {% else %}
                            <div class="text-center text-muted py-5">
                                <i class="bi bi-graph-up fs-1"></i><br>
                                No activity data yet
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Recent Referrals -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Recent Referrals</h5>
                        </div>
                        <div class="card-body">
                            {% if stats.recent_referrals %}
                            <div class="list-group list-group-flush">
                                {% for referral in stats.recent_referrals %}
                                <div class="list-group-item px-0">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <div class="fw-bold">{{ referral.fingerprint }}</div>
                                            <small class="text-muted">{{ referral.ip }}</small>
                                        </div>
                                        <small class="text-muted">{{ referral.timestamp }}</small>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            {% else %}
                            <div class="text-center text-muted">
                                <i class="bi bi-clock"></i><br>
                                No referrals yet
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Referral Link -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Your Referral Link</h5>
                        </div>
                        <div class="card-body">
                            <div class="input-group">
                                <input type="text" class="form-control" id="referralLink" 
                                       value="{{ request.url_root }}?ref={{ stats.code }}" readonly>
                                <button class="btn btn-primary" onclick="copyLink()" title="Copy link">
                                    <i class="bi bi-clipboard"></i> Copy Link
                                </button>
                            </div>
                            <div class="mt-3">
                                <h6>Share on Social Media:</h6>
                                <div class="btn-group">
                                    <button class="btn btn-outline-primary" onclick="shareTwitter()">
                                        <i class="bi bi-twitter"></i> Twitter
                                    </button>
                                    <button class="btn btn-outline-primary" onclick="shareFacebook()">
                                        <i class="bi bi-facebook"></i> Facebook
                                    </button>
                                    <button class="btn btn-outline-primary" onclick="shareLinkedIn()">
                                        <i class="bi bi-linkedin"></i> LinkedIn
                                    </button>
                                    <button class="btn btn-outline-primary" onclick="shareEmail()">
                                        <i class="bi bi-envelope"></i> Email
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Instructions -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">How to Maximize Your Referrals</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="text-center mb-3">
                                        <i class="bi bi-share text-primary" style="font-size: 2rem;"></i>
                                        <h6 class="mt-2">Share Widely</h6>
                                        <p class="text-muted small">Share your link on social media, forums, and with friends</p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center mb-3">
                                        <i class="bi bi-chat-dots text-success" style="font-size: 2rem;"></i>
                                        <h6 class="mt-2">Engage Community</h6>
                                        <p class="text-muted small">Participate in discussions and provide value to users</p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center mb-3">
                                        <i class="bi bi-graph-up text-info" style="font-size: 2rem;"></i>
                                        <h6 class="mt-2">Track Progress</h6>
                                        <p class="text-muted small">Monitor your stats and optimize your sharing strategy</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Initialize activity chart
{% if stats.daily_stats %}
const ctx = document.getElementById('activityChart').getContext('2d');
const chartData = {
    labels: [{% for day in stats.daily_stats %}'{{ day.date }}'{% if not loop.last %},{% endif %}{% endfor %}],
    datasets: [{
        label: 'Referrals',
        data: [{% for day in stats.daily_stats %}{{ day.count }}{% if not loop.last %},{% endif %}{% endfor %}],
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        tension: 0.1
    }]
};

new Chart(ctx, {
    type: 'line',
    data: chartData,
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
{% endif %}

// Copy link function
function copyLink() {
    const linkInput = document.getElementById('referralLink');
    linkInput.select();
    navigator.clipboard.writeText(linkInput.value).then(function() {
        const btn = event.target;
        const originalHTML = btn.innerHTML;
        btn.innerHTML = '<i class="bi bi-check text-success"></i> Copied!';
        setTimeout(() => {
            btn.innerHTML = originalHTML;
        }, 2000);
        
        showToast('Referral link copied to clipboard!', 'success');
    });
}

// Social sharing functions
function shareTwitter() {
    const url = document.getElementById('referralLink').value;
    const text = 'Check out PEPE Store - Top #1 Tools for PC!';
    window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`, '_blank');
}

function shareFacebook() {
    const url = document.getElementById('referralLink').value;
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`, '_blank');
}

function shareLinkedIn() {
    const url = document.getElementById('referralLink').value;
    const title = 'PEPE Store - Top #1 Tools for PC';
    window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}&title=${encodeURIComponent(title)}`, '_blank');
}

function shareEmail() {
    const url = document.getElementById('referralLink').value;
    const subject = 'Check out PEPE Store';
    const body = `I thought you might be interested in PEPE Store - Top #1 Tools for PC!\n\n${url}`;
    window.open(`mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`);
}

// Toast notification function
function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toast-container') || createToastContainer();
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    toastContainer.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toast-container';
    container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
    document.body.appendChild(container);
    return container;
}
</script>
{% endblock %}
