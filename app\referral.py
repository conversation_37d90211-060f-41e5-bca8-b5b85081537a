"""
Publisher & Admin Referral System
Implements referral tracking with unique-user counting, publisher stats, and admin-generated campaign links
"""

import secrets
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, render_template, redirect, url_for, flash, session, make_response
from app.models import get_db
from app.utils.common import get_client_ip
from app.utils.auth import require_admin

referral_bp = Blueprint('referral', __name__)

class ReferralManager:
    """Manages referral tracking and analytics"""
    
    @staticmethod
    def create_publisher(code, name, created_by=None):
        """Create a new publisher with unique code"""
        if not code or not name:
            return False, "Code and name are required"
        
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Check if code already exists
            cursor.execute('SELECT 1 FROM publishers WHERE code = ?', (code,))
            if cursor.fetchone():
                return False, "Publisher code already exists"
            
            # Create publisher
            cursor.execute('''
                INSERT INTO publishers (code, name, created_by, created_at)
                VALUES (?, ?, ?, ?)
            ''', (code, name, created_by, datetime.now()))
            
            conn.commit()
            return True, "Publisher created successfully"
    
    @staticmethod
    def create_admin_link(code, description, created_by=None):
        """Create an admin-generated campaign link"""
        if not code or not description:
            return False, "Code and description are required"
        
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Check if code already exists
            cursor.execute('SELECT 1 FROM admin_links WHERE code = ?', (code,))
            if cursor.fetchone():
                return False, "Admin link code already exists"
            
            # Create admin link
            cursor.execute('''
                INSERT INTO admin_links (code, description, created_by, created_at)
                VALUES (?, ?, ?, ?)
            ''', (code, description, created_by, datetime.now()))
            
            conn.commit()
            return True, "Admin link created successfully"
    
    @staticmethod
    def track_referral(code, fingerprint, ip_address):
        """Track a referral visit (unique per code+fingerprint combination)"""
        if not code or not fingerprint:
            return False, "Code and fingerprint are required"
        
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Check if this combination already exists
            cursor.execute('SELECT 1 FROM referrals WHERE code = ? AND fingerprint = ?', (code, fingerprint))
            if cursor.fetchone():
                return False, "Referral already tracked for this user"
            
            # Add new referral
            cursor.execute('''
                INSERT INTO referrals (code, fingerprint, first_ip, first_seen)
                VALUES (?, ?, ?, ?)
            ''', (code, fingerprint, ip_address, datetime.now()))
            
            conn.commit()
            return True, "Referral tracked successfully"
    
    @staticmethod
    def get_referral_stats(code):
        """Get referral statistics for a specific code"""
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Get total unique referrals
            cursor.execute('SELECT COUNT(*) FROM referrals WHERE code = ?', (code,))
            total_referrals = cursor.fetchone()[0]
            
            # Get referrals by date (last 30 days)
            cursor.execute('''
                SELECT DATE(first_seen) as date, COUNT(*) as count
                FROM referrals 
                WHERE code = ? AND first_seen >= date('now', '-30 days')
                GROUP BY DATE(first_seen)
                ORDER BY date DESC
            ''', (code,))
            daily_stats = cursor.fetchall()
            
            # Get recent referrals
            cursor.execute('''
                SELECT fingerprint, first_ip, first_seen
                FROM referrals 
                WHERE code = ?
                ORDER BY first_seen DESC
                LIMIT 10
            ''', (code,))
            recent_referrals = cursor.fetchall()
            
            return {
                'code': code,
                'total_referrals': total_referrals,
                'daily_stats': [{'date': row[0], 'count': row[1]} for row in daily_stats],
                'recent_referrals': [
                    {
                        'fingerprint': row[0][:16] + '...',  # Truncate for privacy
                        'ip': row[1],
                        'timestamp': row[2]
                    } for row in recent_referrals
                ]
            }
    
    @staticmethod
    def get_all_referral_stats():
        """Get referral statistics for all codes"""
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Get publisher stats
            cursor.execute('''
                SELECT p.code, p.name, COUNT(r.id) as referral_count
                FROM publishers p
                LEFT JOIN referrals r ON p.code = r.code
                GROUP BY p.code, p.name
                ORDER BY referral_count DESC
            ''')
            publisher_stats = cursor.fetchall()
            
            # Get admin link stats
            cursor.execute('''
                SELECT a.code, a.description, COUNT(r.id) as referral_count
                FROM admin_links a
                LEFT JOIN referrals r ON a.code = r.code
                GROUP BY a.code, a.description
                ORDER BY referral_count DESC
            ''')
            admin_link_stats = cursor.fetchall()
            
            return {
                'publishers': [
                    {'code': row[0], 'name': row[1], 'referrals': row[2]}
                    for row in publisher_stats
                ],
                'admin_links': [
                    {'code': row[0], 'description': row[1], 'referrals': row[2]}
                    for row in admin_link_stats
                ]
            }
    
    @staticmethod
    def generate_referral_code(prefix='REF'):
        """Generate a unique referral code"""
        return f"{prefix}_{secrets.token_urlsafe(8)}"

class ReferralMiddleware:
    """Middleware to handle referral tracking"""
    
    @staticmethod
    def process_referral_cookie():
        """Process referral parameter and set cookie"""
        ref_code = request.args.get('ref')
        if ref_code:
            # Validate referral code exists
            with get_db() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT 1 FROM publishers WHERE code = ?
                    UNION
                    SELECT 1 FROM admin_links WHERE code = ?
                ''', (ref_code, ref_code))
                
                if cursor.fetchone():
                    # Set referral cookie for 30 days
                    response = make_response()
                    response.set_cookie(
                        'ref_code',
                        ref_code,
                        max_age=30*24*60*60,  # 30 days
                        samesite='Lax',
                        secure=request.is_secure
                    )
                    return response
        return None
    
    @staticmethod
    def track_referral_if_needed():
        """Track referral if cookie exists and fingerprint is available"""
        ref_code = request.cookies.get('ref_code')
        fingerprint = request.cookies.get('fingerprint')
        
        if ref_code and fingerprint:
            ip_address = get_client_ip()
            success, message = ReferralManager.track_referral(ref_code, fingerprint, ip_address)
            # Note: We don't need to handle the result here, just track silently
        
        return None

# Admin routes for referral management
@referral_bp.route('/admin/referrals')
@require_admin
def admin_referral_dashboard():
    """Admin dashboard for all referral statistics"""
    stats = ReferralManager.get_all_referral_stats()
    return render_template('admin/referral_dashboard.html', stats=stats)

@referral_bp.route('/admin/create-publisher', methods=['GET', 'POST'])
@require_admin
def admin_create_publisher():
    """Admin page to create new publisher"""
    if request.method == 'POST':
        code = request.form.get('code', '').strip()
        name = request.form.get('name', '').strip()
        
        if not code:
            code = ReferralManager.generate_referral_code('PUB')
        
        created_by = session.get('username', 'Admin')
        success, message = ReferralManager.create_publisher(code, name, created_by)
        
        if success:
            flash(f'Publisher "{name}" created with code: {code}', 'success')
            return redirect(url_for('referral.admin_referral_dashboard'))
        else:
            flash(f'Failed to create publisher: {message}', 'error')
    
    return render_template('admin/create_publisher.html')

@referral_bp.route('/admin/create-link', methods=['GET', 'POST'])
@require_admin
def admin_create_link():
    """Admin page to create campaign link"""
    if request.method == 'POST':
        code = request.form.get('code', '').strip()
        description = request.form.get('description', '').strip()
        
        if not code:
            code = ReferralManager.generate_referral_code('CAMP')
        
        created_by = session.get('username', 'Admin')
        success, message = ReferralManager.create_admin_link(code, description, created_by)
        
        if success:
            flash(f'Campaign link created with code: {code}', 'success')
            return redirect(url_for('referral.admin_referral_dashboard'))
        else:
            flash(f'Failed to create campaign link: {message}', 'error')
    
    return render_template('admin/create_link.html')

# Publisher routes
@referral_bp.route('/pub/<code>/stats')
def publisher_stats(code):
    """Public stats page for publishers (their code only)"""
    # Verify the code exists as a publisher
    with get_db() as conn:
        cursor = conn.cursor()
        cursor.execute('SELECT name FROM publishers WHERE code = ?', (code,))
        publisher = cursor.fetchone()
        
        if not publisher:
            return "Publisher not found", 404
    
    stats = ReferralManager.get_referral_stats(code)
    stats['publisher_name'] = publisher[0]
    
    return render_template('publisher/referral_stats.html', stats=stats)

# API routes
@referral_bp.route('/api/referral-stats/<code>')
def api_referral_stats(code):
    """API endpoint to get referral statistics"""
    stats = ReferralManager.get_referral_stats(code)
    return jsonify(stats)

@referral_bp.route('/api/create-publisher', methods=['POST'])
@require_admin
def api_create_publisher():
    """API endpoint to create publisher"""
    data = request.get_json()
    if not data:
        return jsonify({'success': False, 'error': 'Invalid JSON data'}), 400
    
    code = data.get('code', '').strip()
    name = data.get('name', '').strip()
    
    if not name:
        return jsonify({'success': False, 'error': 'Name is required'}), 400
    
    if not code:
        code = ReferralManager.generate_referral_code('PUB')
    
    created_by = session.get('username', 'API Admin')
    success, message = ReferralManager.create_publisher(code, name, created_by)
    
    return jsonify({
        'success': success,
        'message': message,
        'code': code if success else None
    }), 200 if success else 400

@referral_bp.route('/api/create-link', methods=['POST'])
@require_admin
def api_create_link():
    """API endpoint to create admin link"""
    data = request.get_json()
    if not data:
        return jsonify({'success': False, 'error': 'Invalid JSON data'}), 400
    
    code = data.get('code', '').strip()
    description = data.get('description', '').strip()
    
    if not description:
        return jsonify({'success': False, 'error': 'Description is required'}), 400
    
    if not code:
        code = ReferralManager.generate_referral_code('CAMP')
    
    created_by = session.get('username', 'API Admin')
    success, message = ReferralManager.create_admin_link(code, description, created_by)
    
    return jsonify({
        'success': success,
        'message': message,
        'code': code if success else None
    }), 200 if success else 400

# Middleware functions to be called from main app
def handle_referral_parameter():
    """Handle ?ref= parameter and set cookie"""
    return ReferralMiddleware.process_referral_cookie()

def track_referral():
    """Track referral if conditions are met"""
    return ReferralMiddleware.track_referral_if_needed()
