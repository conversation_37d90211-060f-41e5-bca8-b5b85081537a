/**
 * Iron-clad Fingerprinting Client
 * Collects 20+ entropy signals and sends raw data to server
 * Implements nonce-based replay defense and tamper-proof headers
 */
function xorEncrypt(text, key) {
  const textBytes = new TextEncoder().encode(text);
  const keyBytes = new TextEncoder().encode(key);
  const result = new Uint8Array(textBytes.length);

  for (let i = 0; i < textBytes.length; i++) {
    result[i] = textBytes[i] ^ keyBytes[i % keyBytes.length];
  }
  // Convert to base64
  return btoa(String.fromCharCode(...result));
}

function xorDecrypt(base64, key) {
  const encryptedStr = atob(base64);
  const encryptedBytes = Uint8Array.from(encryptedStr, c => c.charCodeAt(0));
  const keyBytes = new TextEncoder().encode(key);
  const result = new Uint8Array(encryptedBytes.length);

  for (let i = 0; i < encryptedBytes.length; i++) {
    result[i] = encryptedBytes[i] ^ keyBytes[i % keyBytes.length];
  }
  return new TextDecoder().decode(result);
}
class SecureFingerprinter {
  constructor() {
    this.currentNonce = null;
    this.fingerprint = null;
  }

  async getNonce() {
    try {
      const response = await fetch('/fp/nonce');
      const data = await response.json();
      return data.nonce;
    } catch (e) {
      console.error('Failed to get nonce:', e);
      return null;
    }
  }

  async initialize() {
    try {
      // Get nonce first
      this.currentNonce = await this.getNonce();
      if (!this.currentNonce) {
        throw new Error('Failed to obtain nonce');
      }

      const fingerp = await ThumbmarkJS.getFingerprint();
      const fingerpdata = await ThumbmarkJS.getFingerprintData();
      const fbCipher   = await xorEncrypt(fingerp,   this.currentNonce.split('-')[0]);
      const omgCipher  = await xorEncrypt(JSON.stringify(fingerpdata), this.currentNonce.split('-')[0]);

      const response = await fetch('/fp/collect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          fb: fbCipher,
          fbdata: omgCipher,
          nonce: this.currentNonce
        })
      });

      const result = await response.json();

      if (result.success) {
        this.fingerprint = xorDecrypt(result.fingerprint, this.currentNonce.split('-')[0]);
        this.setupHeaderInterceptor();
        return true;
      } else {
        console.error('Fingerprinting failed:', result.error);
        return false;
      }
    } catch (error) {
      console.error('Fingerprinting initialization error:', error);
      return false;
    }
  }

  setupHeaderInterceptor() {
    // Intercept all fetch requests to add fingerprint header
    const originalFetch = window.fetch;
    const fingerprint = this.fingerprint;

    window.fetch = function(url, options = {}) {
      options.headers = options.headers || {};
      options.headers['X-Visitor-FP'] = fingerprint;
      return originalFetch(url, options);
    };

    // Intercept XMLHttpRequest
    const originalOpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function() {
      originalOpen.apply(this, arguments);
      this.setRequestHeader('X-Visitor-FP', fingerprint);
    };
  }
}

// Auto-initialize when DOM is ready
(async () => {
  try {
    const fingerprinter = new SecureFingerprinter();
    const success = await fingerprinter.initialize();

    if (success) {
      // Stop progress animation if available
      if (window.stopProgress) {
        window.stopProgress();
      }

      // Get the next URL from the global variable set by the template
      const nextUrl = window.NEXT_URL || '/';

      setTimeout(() => {
        window.location.href = nextUrl;
      }, 500);
    } else {
      alert("Fingerprint verification failed. Please try again.");
      window.location.href = "/";
    }
  } catch (error) {
    console.error('Fingerprinting error:', error);
    alert("An error occurred during verification. Please try again.");
    window.location.href = "/";
  }
})();
