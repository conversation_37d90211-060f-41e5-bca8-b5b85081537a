{% extends "base.html" %}

{% block title %}Referral Analytics - PEPE Store Admin{% endblock %}

{% block content %}
<div class="container-fluid my-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-graph-up text-success"></i> Referral Analytics</h2>
                <div>
                    <a href="{{ url_for('referral.admin_create_publisher') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Add Publisher
                    </a>
                    <a href="{{ url_for('referral.admin_create_link') }}" class="btn btn-outline-primary">
                        <i class="bi bi-link-45deg"></i> Create Campaign Link
                    </a>
                </div>
            </div>

            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Total Publishers</h6>
                                    <h3 class="mb-0">{{ stats.publishers|length }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-people fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Campaign Links</h6>
                                    <h3 class="mb-0">{{ stats.admin_links|length }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-link-45deg fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Total Referrals</h6>
                                    <h3 class="mb-0">
                                        {{ (stats.publishers|sum(attribute='referrals') + stats.admin_links|sum(attribute='referrals')) }}
                                    </h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-graph-up fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-dark">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Active Codes</h6>
                                    <h3 class="mb-0">
                                        {{ (stats.publishers|selectattr('referrals', '>', 0)|list|length + 
                                             stats.admin_links|selectattr('referrals', '>', 0)|list|length) }}
                                    </h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-check-circle fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Publishers -->
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Publishers</h5>
                            <a href="{{ url_for('referral.admin_create_publisher') }}" class="btn btn-sm btn-primary">
                                <i class="bi bi-plus"></i> Add
                            </a>
                        </div>
                        <div class="card-body p-0">
                            {% if stats.publishers %}
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Code</th>
                                            <th>Name</th>
                                            <th>Referrals</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for pub in stats.publishers|sort(attribute='referrals', reverse=true) %}
                                        <tr>
                                            <td>
                                                <code class="text-primary">{{ pub.code }}</code>
                                                <button class="btn btn-sm btn-outline-secondary ms-1" 
                                                        onclick="copyToClipboard('{{ request.url_root }}?ref={{ pub.code }}')" 
                                                        title="Copy referral link">
                                                    <i class="bi bi-clipboard"></i>
                                                </button>
                                            </td>
                                            <td>{{ pub.name }}</td>
                                            <td>
                                                <span class="badge bg-{{ 'success' if pub.referrals > 0 else 'secondary' }}">
                                                    {{ pub.referrals }}
                                                </span>
                                            </td>
                                            <td>
                                                <a href="{{ url_for('referral.publisher_stats', code=pub.code) }}" 
                                                   class="btn btn-sm btn-outline-info" title="View Stats">
                                                    <i class="bi bi-graph-up"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="p-4 text-center text-muted">
                                <i class="bi bi-people fs-1"></i><br>
                                No publishers yet
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Campaign Links -->
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Campaign Links</h5>
                            <a href="{{ url_for('referral.admin_create_link') }}" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-plus"></i> Create
                            </a>
                        </div>
                        <div class="card-body p-0">
                            {% if stats.admin_links %}
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Code</th>
                                            <th>Description</th>
                                            <th>Referrals</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for link in stats.admin_links|sort(attribute='referrals', reverse=true) %}
                                        <tr>
                                            <td>
                                                <code class="text-success">{{ link.code }}</code>
                                                <button class="btn btn-sm btn-outline-secondary ms-1" 
                                                        onclick="copyToClipboard('{{ request.url_root }}?ref={{ link.code }}')" 
                                                        title="Copy campaign link">
                                                    <i class="bi bi-clipboard"></i>
                                                </button>
                                            </td>
                                            <td>{{ link.description }}</td>
                                            <td>
                                                <span class="badge bg-{{ 'success' if link.referrals > 0 else 'secondary' }}">
                                                    {{ link.referrals }}
                                                </span>
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-info" 
                                                        onclick="viewStats('{{ link.code }}')" title="View Stats">
                                                    <i class="bi bi-graph-up"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="p-4 text-center text-muted">
                                <i class="bi bi-link-45deg fs-1"></i><br>
                                No campaign links yet
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Stats Modal -->
<div class="modal fade" id="statsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Referral Statistics</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="statsContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Copy to clipboard function
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        const btn = event.target.closest('button');
        const originalHTML = btn.innerHTML;
        btn.innerHTML = '<i class="bi bi-check text-success"></i>';
        setTimeout(() => {
            btn.innerHTML = originalHTML;
        }, 1000);
        
        // Show toast notification
        showToast('Referral link copied to clipboard!', 'success');
    });
}

// View stats function
async function viewStats(code) {
    const modal = new bootstrap.Modal(document.getElementById('statsModal'));
    const content = document.getElementById('statsContent');
    
    modal.show();
    
    try {
        const response = await fetch(`/api/referral-stats/${code}`);
        const stats = await response.json();
        
        content.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h3>${stats.total_referrals}</h3>
                            <p class="mb-0">Total Referrals</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h3>${stats.daily_stats.length}</h3>
                            <p class="mb-0">Active Days</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-4">
                <h6>Recent Activity (Last 30 days)</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Referrals</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${stats.daily_stats.map(day => `
                                <tr>
                                    <td>${day.date}</td>
                                    <td><span class="badge bg-primary">${day.count}</span></td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
            
            <div class="mt-4">
                <h6>Recent Referrals</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Fingerprint</th>
                                <th>IP</th>
                                <th>Timestamp</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${stats.recent_referrals.map(ref => `
                                <tr>
                                    <td><code>${ref.fingerprint}</code></td>
                                    <td>${ref.ip}</td>
                                    <td>${ref.timestamp}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    } catch (error) {
        content.innerHTML = `
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i> 
                Error loading statistics: ${error.message}
            </div>
        `;
    }
}

// Toast notification function
function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toast-container') || createToastContainer();
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    toastContainer.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toast-container';
    container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
    document.body.appendChild(container);
    return container;
}
</script>
{% endblock %}
