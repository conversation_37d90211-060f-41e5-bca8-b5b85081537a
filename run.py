#!/usr/bin/env python3
"""
App Store - Run Script
This script starts the Flask application with proper configuration.
"""

import os
import sys
sys.dont_write_bytecode = True
from app import create_app
from app.config import Config
# Ensure SECRET_KEY is set
if not os.environ.get('SECRET_KEY'):
     os.environ['SECRET_KEY'] = Config.SECRET_KEY
app = create_app()

def main():
    """Main function to run the Flask application"""

    # Database will be auto-initialized by the app factory
    print("✓ Database initialization handled by app factory")

    # Check if running in development or production
    debug_mode = (
        os.environ.get('FLASK_ENV') == 'development' or
        os.environ.get('FLASK_DEBUG') == '1' or
        '--debug' in sys.argv
    )

    # Get port from environment or default to 5000
    port = int(os.environ.get('PORT', 5000))
    host = os.environ.get('HOST', '0.0.0.0')

    print("\n" + "="*50)
    print("🏪 PEPE STORE - Starting Server")
    print("="*50)
    print(f"Debug Mode: {'ON' if debug_mode else 'OFF'}")
    print(f"Host: {host}")
    print(f"Port: {port}")
    print(f"URL: http://localhost:{port}")
    print("\nDefault Admin Credentials:")
    print(f"Username: {Config.ADMIN_USERNAME}")
    print(f"Password: {Config.ADMIN_PASSWORD}")
    if Config.ADMIN_PASSWORD == 'admin123':
        print("⚠️  WARNING: Using default password! Change in production!")
    print("="*50)
    print("Press Ctrl+C to stop the server")
    print("="*50 + "\n")

    try:
        # Run the Flask application
        app.run(
            debug=debug_mode,
            host=host,
            port=port,
            threaded=True,
            use_reloader=debug_mode,
            # ssl_context=('cert.pem', 'key.pem')
        )
    except KeyboardInterrupt:
        print("\n\n" + "="*50)
        print("🛑 Server stopped by user")
        print("="*50)
    except Exception as e:
        print(f"\n❌ Error starting server: {e}")
        print("Check if port is already in use or permissions are correct")
        sys.exit(1)








from threading import Thread
from telegram import InlineKeyboardButton, InlineKeyboardMarkup, Update, ParseMode
from telegram.ext import (
    Updater, CommandHandler, MessageHandler, Filters,
    CallbackQueryHandler, CallbackContext, ConversationHandler
)
import time
import json
import os


BOT_TOKEN = '7616809969:AAHW5THTL_Fyf5BAr7hyNo9xGsyJwKeKV3w'
ADMIN_ID = 1552488586  # Replace with your Telegram user ID

# === States ===
SELECT_PLATFORM, DISCORD_USERNAME, EMAIL, USEFULNESS, LINKS, MALWARE_HISTORY, TRUST_REASON, COMMENTS = range(8)

applicants = {}
applied_users = set()
APPLICATIONS_FILE = "applications.json"

# === Load existing data if exists ===
if os.path.exists(APPLICATIONS_FILE):
    with open(APPLICATIONS_FILE, "r", encoding="utf-8") as f:
        loaded = json.load(f)
        applied_users = set(int(uid) for uid in loaded.keys())
        applicants = loaded

def save_applications():
    with open(APPLICATIONS_FILE, "w", encoding="utf-8") as f:
        json.dump(applicants, f, indent=2)


# === Telegram Bot Handlers ===

def start(update: Update, context: CallbackContext):
    update.message.reply_text(
        "👋 Welcome to the pepe store publisher application bot!\n\n"
        "If you'd like to apply to be a publisher on our platform, please use the /apply command."
    )

def apply(update: Update, context: CallbackContext):
    user_id = update.effective_user.id
    if str(user_id) in applicants:
        update.message.reply_text("❌ You have already submitted an application.")
        return ConversationHandler.END

    keyboard = [
        [InlineKeyboardButton("Apply with Telegram", callback_data='telegram')],
        [InlineKeyboardButton("Apply with Discord", callback_data='discord')]
    ]
    update.message.reply_text("Choose how you want to apply:", reply_markup=InlineKeyboardMarkup(keyboard))
    return SELECT_PLATFORM

def select_platform(update: Update, context: CallbackContext):
    query = update.callback_query
    query.answer()
    user_id = query.from_user.id
    context.user_data['platform'] = query.data

    if query.data == 'telegram':
        context.user_data['username'] = query.from_user.username or "Unknown"
        query.edit_message_text("Please enter your email address:")
        return EMAIL
    else:
        query.edit_message_text("Enter your Discord username:")
        return DISCORD_USERNAME

def get_discord_username(update: Update, context: CallbackContext):
    context.user_data['username'] = update.message.text.strip()
    update.message.reply_text("Please enter your email address:")
    return EMAIL

def get_email(update: Update, context: CallbackContext):
    context.user_data['email'] = update.message.text.strip()
    update.message.reply_text("How are you going to be useful to our platform?")
    return USEFULNESS

def get_usefulness(update: Update, context: CallbackContext):
    context.user_data['usefulness'] = update.message.text.strip()
    update.message.reply_text("🔗 Please provide links to your previous work (GitHub, YouTube, etc.):")
    return LINKS

def get_links(update: Update, context: CallbackContext):
    context.user_data['links'] = update.message.text.strip()
    update.message.reply_text("❓ Have you ever posted malware or misleading content? Please explain:")
    return MALWARE_HISTORY

def get_malware_history(update: Update, context: CallbackContext):
    context.user_data['malware_history'] = update.message.text.strip()
    update.message.reply_text("🛡️ Why should we trust you with publishing privileges?")
    return TRUST_REASON

def get_trust_reason(update: Update, context: CallbackContext):
    context.user_data['trust_reason'] = update.message.text.strip()
    update.message.reply_text("💬 Any additional comments or requests?")
    return COMMENTS

def get_comments(update: Update, context: CallbackContext):
    user_id = update.effective_user.id
    context.user_data['comments'] = update.message.text.strip()

    # Save application
    applied_users.add(user_id)
    applicants[str(user_id)] = context.user_data
    save_applications()

    data = context.user_data
    platform = data['platform'].capitalize()

    msg = (
        f"📥 <b>New Publisher Application</b>\n\n"
        f"🌐 <b>Platform:</b> {platform}\n"
        f"👤 <b>Username:</b> {data['username']}\n"
        f"📧 <b>Email:</b> {data['email']}\n"
        f"📝 <b>Usefulness:</b> {data['usefulness']}\n"
        f"🔗 <b>Links:</b> {data['links']}\n"
        f"⚠️ <b>Malware History:</b> {data['malware_history']}\n"
        f"🛡️ <b>Trust Reason:</b> {data['trust_reason']}\n"
        f"💬 <b>Comments:</b> {data['comments']}\n"
        f"🆔 <b>User ID:</b> <code>{user_id}</code>\n\n"
        f"Choose an action below:"
    )

    buttons = [
        [
            InlineKeyboardButton("✅ Approve", callback_data=f"approve_{user_id}"),
            InlineKeyboardButton("❌ Reject", callback_data=f"reject_{user_id}")
        ]
    ]
    context.bot.send_message(chat_id=ADMIN_ID, text=msg, parse_mode=ParseMode.HTML, reply_markup=InlineKeyboardMarkup(buttons))
    update.message.reply_text("✅ Your application has been submitted successfully!")
    return ConversationHandler.END

def handle_decision(update: Update, context: CallbackContext):
    query = update.callback_query
    query.answer()
    action, user_id = query.data.split("_")
    user_id = int(user_id)

    if action == "approve":
        context.bot.send_message(chat_id=user_id, text="✅ Your application has been approved!")
        query.edit_message_text("✅ Application approved.")
    elif action == "reject":
        context.bot.send_message(chat_id=user_id, text="❌ Your application has been rejected.")
        query.edit_message_text("❌ Application rejected.")

def cancel(update: Update, context: CallbackContext):
    update.message.reply_text("Application cancelled.")
    return ConversationHandler.END

# === Bot Thread ===
def run_bot():
    updater = Updater(BOT_TOKEN, use_context=True)
    dp = updater.dispatcher

    # Commands
    dp.add_handler(CommandHandler("start", start))
    dp.add_handler(CommandHandler("apply", apply))

    # Conversation flow
    conv_handler = ConversationHandler(
        entry_points=[CommandHandler("apply", apply)],
        states={
            SELECT_PLATFORM: [CallbackQueryHandler(select_platform)],
            DISCORD_USERNAME: [MessageHandler(Filters.text & ~Filters.command, get_discord_username)],
            EMAIL: [MessageHandler(Filters.text & ~Filters.command, get_email)],
            USEFULNESS: [MessageHandler(Filters.text & ~Filters.command, get_usefulness)],
            LINKS: [MessageHandler(Filters.text & ~Filters.command, get_links)],
            MALWARE_HISTORY: [MessageHandler(Filters.text & ~Filters.command, get_malware_history)],
            TRUST_REASON: [MessageHandler(Filters.text & ~Filters.command, get_trust_reason)],
            COMMENTS: [MessageHandler(Filters.text & ~Filters.command, get_comments)],
        },
        fallbacks=[CommandHandler("cancel", cancel)]
    )

    dp.add_handler(conv_handler)
    dp.add_handler(CallbackQueryHandler(handle_decision, pattern=r"^(approve|reject)_\d+$"))

    updater.start_polling()
    while True:
        time.sleep(10)



if __name__ == '__main__':
    # Thread(target=run_bot).start()
    main()
