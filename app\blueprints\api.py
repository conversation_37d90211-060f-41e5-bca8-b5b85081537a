"""
API blueprint for REST API endpoints
"""
from flask import Blueprint, request, jsonify
from app.models import App, User

api_bp = Blueprint('api', __name__)


@api_bp.route('/apps')
def get_apps():
    """Get apps via API"""
    try:
        category = request.args.get('category', '').strip()
        search = request.args.get('search', '').strip()
        limit = min(100, max(1, request.args.get('limit', 20, type=int)))  # Limit between 1-100

        apps = App.get_all(limit=limit, category=category, search=search)
        return jsonify({
            'success': True,
            'apps': apps,
            'count': len(apps)
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@api_bp.route('/app/<int:app_id>')
def get_app(app_id):
    """Get single app via API"""
    try:
        if app_id <= 0:
            return jsonify({'success': False, 'error': 'Invalid app ID'}), 400

        app = App.get_by_id(app_id)
        if not app:
            return jsonify({'success': False, 'error': 'App not found'}), 404

        return jsonify({'success': True, 'app': app})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


# Rating functionality moved to secure rating system in rating.py
# Use /api/rate/<int:item_id> endpoint from rating blueprint instead


@api_bp.route('/stats')
def get_stats():
    """Get basic stats"""
    try:
        total_apps = App.get_count()
        total_users = len(User.get_all())
        categories = App.get_categories()
        featured_count = App.get_count(featured_only=True)

        return jsonify({
            'success': True,
            'stats': {
                'total_apps': total_apps,
                'total_users': total_users,
                'featured_apps': featured_count,
                'categories': categories,
                'category_count': len(categories)
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


# Test route for API blueprint
@api_bp.route('/test')
def test_api():
    """Test route to verify API blueprint loads"""
    return jsonify({'blueprint': 'api', 'status': 'working', 'routes': ['/apps', '/app/<id>', '/app/<id>/rate', '/stats']})
