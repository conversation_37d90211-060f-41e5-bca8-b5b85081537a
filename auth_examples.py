#!/usr/bin/env python3
"""
Examples of how to use the upgraded Flask-Login authentication system
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_user, logout_user, login_required, current_user
from app.models import User
from app.utils.auth import require_admin, require_publisher_or_admin

# Create example blueprint
example_bp = Blueprint('example', __name__)

# ============================================================================
# LOGIN EXAMPLE
# ============================================================================

@example_bp.route('/login', methods=['GET', 'POST'])
def login():
    """Example login route using Flask-Login"""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        remember_me = request.form.get('remember_me', False)
        
        # Get user data from database
        user_data = User.get_by_username(username)
        
        if user_data and User.check_password(user_data, password) and user_data.get('is_active'):
            # Create User object for Flask-Login
            user_obj = User(user_data)
            
            # Log in user with Flask-Login
            login_user(user_obj, remember=remember_me)
            
            flash(f'Welcome back, {user_data["username"]}!', 'success')
            
            # Handle next parameter for redirect after login
            next_page = request.args.get('next')
            if next_page and next_page.startswith('/'):
                return redirect(next_page)
            
            # Redirect based on role
            if user_obj.is_admin():
                return redirect(url_for('admin.dashboard'))
            else:
                return redirect(url_for('publisher.dashboard'))
        else:
            flash('Invalid credentials', 'error')
    
    return render_template('auth/login.html')

# ============================================================================
# LOGOUT EXAMPLE
# ============================================================================

@example_bp.route('/logout')
@login_required
def logout():
    """Example logout route using Flask-Login"""
    username = current_user.username if current_user.is_authenticated else 'Unknown'
    
    # Logout user with Flask-Login
    logout_user()
    flash('You have been logged out', 'info')
    return redirect(url_for('main.index'))

# ============================================================================
# PROTECTED ROUTE EXAMPLES
# ============================================================================

@example_bp.route('/profile')
@login_required
def profile():
    """Example protected route - requires any authenticated user"""
    return render_template('profile.html', user=current_user)

@example_bp.route('/admin-only')
@require_admin
def admin_only():
    """Example admin-only route"""
    return render_template('admin_only.html', user=current_user)

@example_bp.route('/publisher-or-admin')
@require_publisher_or_admin
def publisher_or_admin():
    """Example route for publishers or admins"""
    return render_template('publisher_or_admin.html', user=current_user)

# ============================================================================
# API ENDPOINT EXAMPLES
# ============================================================================

@example_bp.route('/api/user-info')
@login_required
def api_user_info():
    """Example API endpoint that returns current user info"""
    return jsonify({
        'id': current_user.id,
        'username': current_user.username,
        'email': current_user.email,
        'role': current_user.role,
        'is_admin': current_user.is_admin(),
        'is_publisher': current_user.is_publisher(),
        'is_active': current_user.is_active
    })

@example_bp.route('/api/admin-data')
@require_admin
def api_admin_data():
    """Example admin-only API endpoint"""
    return jsonify({
        'message': 'This is admin-only data',
        'admin_user': current_user.username,
        'timestamp': '2025-01-16'
    })

# ============================================================================
# TEMPLATE EXAMPLES
# ============================================================================

"""
Example template usage with current_user:

<!-- In your Jinja2 templates -->
{% if current_user.is_authenticated %}
    <p>Welcome, {{ current_user.username }}!</p>
    <p>Your role: {{ current_user.role }}</p>
    
    {% if current_user.is_admin() %}
        <a href="{{ url_for('admin.dashboard') }}">Admin Dashboard</a>
    {% endif %}
    
    {% if current_user.is_publisher() %}
        <a href="{{ url_for('publisher.dashboard') }}">Publisher Dashboard</a>
    {% endif %}
    
    <a href="{{ url_for('auth.logout') }}">Logout</a>
{% else %}
    <a href="{{ url_for('auth.login') }}">Login</a>
{% endif %}
"""

# ============================================================================
# CONFIGURATION EXAMPLES
# ============================================================================

"""
Example configuration in config.py:

class Config:
    # Flask-Login Configuration
    REMEMBER_COOKIE_DURATION = timedelta(minutes=30)
    REMEMBER_COOKIE_SECURE = True  # HTTPS only in production
    REMEMBER_COOKIE_HTTPONLY = True
    REMEMBER_COOKIE_SAMESITE = 'Lax'
    
    # Flask-Session Configuration (Redis preferred, filesystem fallback)
    SESSION_TYPE = 'redis' if os.environ.get('REDIS_URL') else 'filesystem'
    SESSION_PERMANENT = True
    SESSION_USE_SIGNER = True
    SESSION_KEY_PREFIX = 'pepe:'
    
    # Enhanced Session Security Settings (30 minutes)
    PERMANENT_SESSION_LIFETIME = timedelta(minutes=30)
    SESSION_COOKIE_SECURE = True  # HTTPS only in production
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    SESSION_COOKIE_NAME = 'pepe_session'
"""

# ============================================================================
# GUNICORN DEPLOYMENT EXAMPLE
# ============================================================================

"""
Example Gunicorn deployment command:

gunicorn --workers 4 --threads 2 --bind 0.0.0.0:5000 --access-logfile - --error-logfile - app:app

Key points for production:
1. Use Redis for session storage (set REDIS_URL environment variable)
2. Set FLASK_ENV=production for secure cookies
3. Use HTTPS in production
4. Set strong SECRET_KEY environment variable
5. Configure proper logging
6. Use --preload flag for better memory usage with multiple workers
"""

# ============================================================================
# MIGRATION NOTES
# ============================================================================

"""
Migration from session-based auth to Flask-Login:

OLD WAY:
    session['user_id'] = user['id']
    session['username'] = user['username']
    session['role'] = user['role']
    session.permanent = True

NEW WAY:
    user_obj = User(user_data)
    login_user(user_obj, remember=remember_me)

OLD WAY:
    if not session.get('user_id'):
        return redirect(url_for('auth.login'))
    
    user_id = session['user_id']
    username = session['username']
    role = session['role']

NEW WAY:
    @login_required
    def protected_route():
        user_id = current_user.id
        username = current_user.username
        role = current_user.role

OLD WAY:
    session.clear()

NEW WAY:
    logout_user()
"""
