#!/usr/bin/env python3
"""
SQLite Migration Helper - Iron-clad Security Upgrade
Safely and idempotently updates database schema for security features:
- Enhanced fingerprinting system
- Ban-by-fingerprint tables
- Referral tracking system
- Secure ratings system
- Security audit tables
"""

import sqlite3
import sys
import os
import argparse
from datetime import datetime

def table_exists(cursor, table_name):
    """Check if table exists in database"""
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
    return cursor.fetchone() is not None

def column_exists(cursor, table_name, column_name):
    """Check if column exists in table"""
    cursor.execute(f"PRAGMA table_info({table_name})")
    return any(row[1] == column_name for row in cursor.fetchall())

def index_exists(cursor, index_name):
    """Check if index exists in database"""
    cursor.execute("SELECT name FROM sqlite_master WHERE type='index' AND name=?", (index_name,))
    return cursor.fetchone() is not None

def constraint_exists(cursor, table_name, constraint_type='UNIQUE'):
    """Check if constraint exists on table"""
    cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
    result = cursor.fetchone()
    if result and result[0]:
        return constraint_type in result[0].upper()
    return False

def update_fingerprints_table(cursor, changes):
    """Update fingerprints table with new security columns"""
    if table_exists(cursor, 'fingerprints'):
        # Add new columns for enhanced fingerprinting
        new_columns = [
            ('raw_entropy_data', 'TEXT'),
            ('ip_address', 'TEXT'),
            ('user_agent', 'TEXT'),
            ('last_seen', 'TIMESTAMP'),
            ('visit_count', 'INTEGER')
        ]

        for col_name, col_def in new_columns:
            if not column_exists(cursor, 'fingerprints', col_name):
                cursor.execute(f'ALTER TABLE fingerprints ADD COLUMN {col_name} {col_def}')
                changes.append(f'Added column {col_name} to fingerprints')

def create_security_tables(cursor, changes):
    """Create all new security-related tables"""

    # Enhanced banned_fingerprints table
    if not table_exists(cursor, 'banned_fingerprints'):
        cursor.execute('''
            CREATE TABLE banned_fingerprints (
                fingerprint TEXT PRIMARY KEY,
                reason TEXT NOT NULL,
                banned_by TEXT,
                created_at TIMESTAMP DEFAULT (datetime('now'))
            )
        ''')
        changes.append('Created table: banned_fingerprints')
    else:
        # Add missing columns
        if not column_exists(cursor, 'banned_fingerprints', 'banned_by'):
            cursor.execute('ALTER TABLE banned_fingerprints ADD COLUMN banned_by TEXT')
            changes.append('Added column banned_by to banned_fingerprints')

    # Nonce tracking table
    if not table_exists(cursor, 'used_nonces'):
        cursor.execute('''
            CREATE TABLE used_nonces (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nonce TEXT UNIQUE NOT NULL,
                fingerprint TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT (datetime('now')),
                expires_at TIMESTAMP NOT NULL
            )
        ''')
        changes.append('Created table: used_nonces')

    # Publishers table
    if not table_exists(cursor, 'publishers'):
        cursor.execute('''
            CREATE TABLE publishers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                created_by TEXT,
                created_at TIMESTAMP DEFAULT (datetime('now'))
            )
        ''')
        changes.append('Created table: publishers')

    # Admin links table
    if not table_exists(cursor, 'admin_links'):
        cursor.execute('''
            CREATE TABLE admin_links (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE NOT NULL,
                description TEXT NOT NULL,
                created_by TEXT,
                created_at TIMESTAMP DEFAULT (datetime('now'))
            )
        ''')
        changes.append('Created table: admin_links')

    # Referrals table (updated structure)
    if not table_exists(cursor, 'referrals'):
        cursor.execute('''
            CREATE TABLE referrals (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT NOT NULL,
                fingerprint TEXT NOT NULL,
                first_ip TEXT,
                first_seen TIMESTAMP DEFAULT (datetime('now')),
                UNIQUE(code, fingerprint)
            )
        ''')
        changes.append('Created table: referrals')
    else:
        # Update old referrals table structure
        if column_exists(cursor, 'referrals', 'publisher_id') and not column_exists(cursor, 'referrals', 'code'):
            # Need to migrate old structure - this is complex, so we'll create new table
            cursor.execute('ALTER TABLE referrals RENAME TO referrals_old')
            cursor.execute('''
                CREATE TABLE referrals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    code TEXT NOT NULL,
                    fingerprint TEXT NOT NULL,
                    first_ip TEXT,
                    first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(code, fingerprint)
                )
            ''')
            changes.append('Migrated referrals table to new structure')

    # Secure ratings table
    if not table_exists(cursor, 'ratings'):
        cursor.execute('''
            CREATE TABLE ratings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                item_id INTEGER NOT NULL,
                fingerprint TEXT NOT NULL,
                rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
                review TEXT,
                ip_address TEXT,
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT (datetime('now')),
                updated_at TIMESTAMP DEFAULT (datetime('now')),
                UNIQUE(item_id, fingerprint)
            )
        ''')
        changes.append('Created table: ratings')

    # Ban events table (enhanced)
    if not table_exists(cursor, 'ban_events'):
        cursor.execute('''
            CREATE TABLE ban_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                fingerprint TEXT NOT NULL,
                event_type TEXT NOT NULL,
                reason TEXT,
                admin_user TEXT,
                ip_address TEXT,
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT (datetime('now'))
            )
        ''')
        changes.append('Created table: ban_events')

    # Rating events table
    if not table_exists(cursor, 'rating_events'):
        cursor.execute('''
            CREATE TABLE rating_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                rating_id INTEGER NOT NULL,
                event_type TEXT NOT NULL,
                description TEXT,
                fingerprint TEXT,
                ip_address TEXT,
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT (datetime('now'))
            )
        ''')
        changes.append('Created table: rating_events')

    # Rate limits table
    if not table_exists(cursor, 'rate_limits'):
        cursor.execute('''
            CREATE TABLE rate_limits (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                identifier TEXT NOT NULL,
                endpoint TEXT NOT NULL,
                request_count INTEGER DEFAULT 1,
                window_start TIMESTAMP DEFAULT (datetime('now'))
            )
        ''')
        changes.append('Created table: rate_limits')

def create_security_indices(cursor, changes):
    """Create indices for performance and security"""
    indices = [
        ('idx_used_nonces_fp', 'used_nonces', 'fingerprint'),
        ('idx_used_nonces_expires', 'used_nonces', 'expires_at'),
        ('idx_referrals_code', 'referrals', 'code'),
        ('idx_referrals_fp', 'referrals', 'fingerprint'),
        ('idx_ratings_item', 'ratings', 'item_id'),
        ('idx_ratings_fp', 'ratings', 'fingerprint'),
        ('idx_ban_events_fp', 'ban_events', 'fingerprint'),
        ('idx_ban_events_type', 'ban_events', 'event_type'),
        ('idx_rating_events_rating', 'rating_events', 'rating_id'),
        ('idx_rate_limits_lookup', 'rate_limits', 'identifier, endpoint, window_start'),
        ('idx_fingerprints_fp', 'fingerprints', 'fingerprint'),
        ('idx_publishers_code', 'publishers', 'code'),
        ('idx_admin_links_code', 'admin_links', 'code')
    ]

    for index_name, table_name, columns in indices:
        if not index_exists(cursor, index_name):
            cursor.execute(f'CREATE INDEX {index_name} ON {table_name}({columns})')
            changes.append(f'Created index: {index_name}')

def main(db_path):
    """Main migration function"""
    changes = []
    conn = sqlite3.connect(db_path)

    try:
        cursor = conn.cursor()
        conn.execute('BEGIN')

        print(f'Starting database migration for: {db_path}')
        print(f'Migration started at: {datetime.now().isoformat()}')

        # Update existing tables
        update_fingerprints_table(cursor, changes)

        # Create new security tables
        create_security_tables(cursor, changes)

        # Create performance indices
        create_security_indices(cursor, changes)

        # Clean up old data (optional)
        cleanup_old_data(cursor, changes)

        conn.commit()

        print('\n=== Migration Summary ===')
        if changes:
            for change in changes:
                print(f' ✓ {change}')
            print(f'\nTotal changes applied: {len(changes)}')
        else:
            print('✓ No changes needed. Schema is up to date.')

        print(f'Migration completed at: {datetime.now().isoformat()}')

    except Exception as e:
        conn.rollback()
        print(f'\n❌ ERROR: Migration failed: {e}')
        print('All changes have been rolled back.')
        sys.exit(1)
    finally:
        conn.close()

def cleanup_old_data(cursor, changes):
    """Clean up old/expired data"""
    # Clean up expired nonces
    cursor.execute('DELETE FROM used_nonces WHERE expires_at < datetime("now")')
    if cursor.rowcount > 0:
        changes.append(f'Cleaned up {cursor.rowcount} expired nonces')

    # Clean up old rate limit entries (older than 24 hours)
    cursor.execute('DELETE FROM rate_limits WHERE window_start < datetime("now", "-1 day")')
    if cursor.rowcount > 0:
        changes.append(f'Cleaned up {cursor.rowcount} old rate limit entries')

if __name__ == '__main__':
    parser = argparse.ArgumentParser(
        description='SQLite Migration Helper - Iron-clad Security Upgrade',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog='''
Examples:
  python update.py                          # Use default database path
  python update.py /path/to/database.db     # Use custom database path
  python update.py --dry-run                # Show what would be changed (not implemented)

This script safely updates your database schema with new security features:
- Enhanced fingerprinting system with entropy data storage
- Ban-by-fingerprint tables with audit logging
- Referral tracking system for publishers and campaigns
- Secure ratings system with fingerprint validation
- Security audit tables and performance indices

The script is idempotent - safe to run multiple times.
        '''
    )

    parser.add_argument(
        'database_path',
        nargs='?',
        default=os.path.join(os.path.dirname(__file__), 'app', 'database.db'),
        help='Path to SQLite database file (default: app/database.db)'
    )

    parser.add_argument(
        '--version',
        action='version',
        version='Iron-clad Security Migration v1.0'
    )

    args = parser.parse_args()

    # Validate database path
    db_path = os.path.abspath(args.database_path)
    db_dir = os.path.dirname(db_path)

    if not os.path.exists(db_dir):
        print(f'❌ ERROR: Database directory does not exist: {db_dir}')
        sys.exit(1)

    # Create database file if it doesn't exist
    if not os.path.exists(db_path):
        print(f'⚠️  Database file does not exist, will be created: {db_path}')
        try:
            # Create empty database file
            conn = sqlite3.connect(db_path)
            conn.close()
        except Exception as e:
            print(f'❌ ERROR: Cannot create database file: {e}')
            sys.exit(1)

    print(f'🔧 Using database: {db_path}')
    print(f'📊 Database size: {os.path.getsize(db_path)} bytes')

    # Run migration
    main(db_path)
