{% extends "base.html" %}

{% block title %}Create Publisher - PEPE Store Admin{% endblock %}

{% block content %}
<div class="container my-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0"><i class="bi bi-person-plus text-primary"></i> Create Publisher</h4>
                        <a href="{{ url_for('referral.admin_referral_dashboard') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Dashboard
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" id="publisherForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Publisher Name *</label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           placeholder="Enter publisher name" required>
                                    <div class="form-text">Display name for the publisher</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="code" class="form-label">Referral Code</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="code" name="code" 
                                               placeholder="Leave empty to auto-generate">
                                        <button type="button" class="btn btn-outline-secondary" 
                                                onclick="generateCode()" title="Generate random code">
                                            <i class="bi bi-arrow-clockwise"></i>
                                        </button>
                                    </div>
                                    <div class="form-text">Unique code for referral tracking. Auto-generated if empty.</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">Referral Link Preview</h6>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="linkPreview" 
                                               value="{{ request.url_root }}?ref=" readonly>
                                        <button type="button" class="btn btn-outline-primary" 
                                                onclick="copyLink()" title="Copy link">
                                            <i class="bi bi-clipboard"></i>
                                        </button>
                                    </div>
                                    <small class="text-muted">This link will be generated after creating the publisher</small>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('referral.admin_referral_dashboard') }}" class="btn btn-secondary">
                                <i class="bi bi-x-circle"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Create Publisher
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Instructions Card -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-info-circle text-info"></i> How It Works</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center mb-3">
                                <i class="bi bi-link-45deg text-primary" style="font-size: 2rem;"></i>
                                <h6 class="mt-2">1. Share Link</h6>
                                <p class="text-muted small">Publisher shares their unique referral link</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center mb-3">
                                <i class="bi bi-person-check text-success" style="font-size: 2rem;"></i>
                                <h6 class="mt-2">2. User Visits</h6>
                                <p class="text-muted small">Unique users are tracked by fingerprint</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center mb-3">
                                <i class="bi bi-graph-up text-info" style="font-size: 2rem;"></i>
                                <h6 class="mt-2">3. Track Stats</h6>
                                <p class="text-muted small">View detailed analytics and statistics</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="bi bi-lightbulb"></i>
                        <strong>Pro Tip:</strong> Publishers can view their statistics at 
                        <code>/pub/[CODE]/stats</code> - this page is publicly accessible.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Update link preview when code changes
document.getElementById('code').addEventListener('input', function() {
    const baseUrl = '{{ request.url_root }}?ref=';
    const preview = document.getElementById('linkPreview');
    preview.value = baseUrl + (this.value || '[CODE]');
});

// Generate random code
function generateCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let code = 'PUB_';
    for (let i = 0; i < 8; i++) {
        code += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    document.getElementById('code').value = code;
    document.getElementById('code').dispatchEvent(new Event('input'));
}

// Copy link to clipboard
function copyLink() {
    const linkInput = document.getElementById('linkPreview');
    linkInput.select();
    navigator.clipboard.writeText(linkInput.value).then(function() {
        const btn = event.target.closest('button');
        const originalHTML = btn.innerHTML;
        btn.innerHTML = '<i class="bi bi-check text-success"></i>';
        setTimeout(() => {
            btn.innerHTML = originalHTML;
        }, 1000);
        
        showToast('Link copied to clipboard!', 'success');
    });
}

// Form validation
document.getElementById('publisherForm').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const code = document.getElementById('code').value.trim();
    
    if (!name) {
        e.preventDefault();
        alert('Please enter a publisher name');
        return;
    }
    
    if (code && code.length < 3) {
        e.preventDefault();
        alert('Referral code must be at least 3 characters long');
        return;
    }
    
    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Creating...';
});

// Toast notification function
function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toast-container') || createToastContainer();
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    toastContainer.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toast-container';
    container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
    document.body.appendChild(container);
    return container;
}

// Initialize link preview
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('code').dispatchEvent(new Event('input'));
});
</script>
{% endblock %}
