"""
Iron-clad Fingerprinting System
Implements bullet-proof server-validated fingerprinting with:
- 20+ entropy signals collection
- Server-side hashing only
- Tamper-proof headers
- Replay defense with nonces
"""

import json
import hashlib
import uuid
import secrets
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, make_response, abort, session
from app.models import get_db
from app.utils.common import get_client_ip
from app.algorithm import encrypt, decrypt

fp_bp = Blueprint('fp', __name__)

class FingerprintManager:
    """Manages secure fingerprinting operations"""
    
    @staticmethod
    def generate_server_fingerprint(entropy_data):
        """
        Generate server-side fingerprint from entropy data
        Uses SHA-256 of sorted entropy values for consistency
        """
        if not entropy_data or not isinstance(entropy_data, dict):
            raise ValueError("Invalid entropy data")
        
        # Extract and sort all entropy values
        sorted_values = []
        for key in sorted(entropy_data.keys()):
            value = entropy_data[key]
            if isinstance(value, (dict, list)):
                value = json.dumps(value, sort_keys=True)
            sorted_values.append(str(value))
        
        # Create fingerprint hash
        fingerprint_string = "|".join(sorted_values)
        fingerprint = hashlib.sha256(fingerprint_string.encode('utf-8')).hexdigest()
        
        return fingerprint
    
    @staticmethod
    def validate_entropy_data(data):
        """Validate that entropy data contains required signals"""
        required_signals = [
            'audio', 'canvas', 'fonts', 'hardware', 'locales', 'permissions',
            'plugins', 'screen', 'system', 'webgl', "math"
        ]
        missing_signals = [signal for signal in required_signals if signal not in data]
        if missing_signals:
            return False, f"Missing required signals: {', '.join(missing_signals)}"
        
        return True, "Valid entropy data"
    
    @staticmethod
    def store_fingerprint(fingerprint, entropy_data, ip_address, user_agent):
        """Store fingerprint in database with metadata"""
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Check if fingerprint already exists
            cursor.execute('SELECT id, visit_count FROM fingerprints WHERE fingerprint = ?', (fingerprint,))
            existing = cursor.fetchone()
            
            if existing:
                # Update existing fingerprint
                cursor.execute('''
                    UPDATE fingerprints 
                    SET last_seen = CURRENT_TIMESTAMP, visit_count = visit_count + 1,
                        ip_address = ?, user_agent = ?
                    WHERE fingerprint = ?
                ''', (ip_address, user_agent, fingerprint))
                return existing[0]
            else:
                # Create new fingerprint
                user_uuid = str(uuid.uuid4())
                hmac_key = secrets.token_hex(32)
                
                cursor.execute('''
                    INSERT INTO fingerprints (uuid, fingerprint, hmac_key, raw_entropy_data, 
                                            ip_address, user_agent)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (user_uuid, fingerprint, hmac_key, json.dumps(entropy_data), 
                      ip_address, user_agent))
                
                conn.commit()
                return cursor.lastrowid
    
    @staticmethod
    def is_fingerprint_banned(fingerprint):
        """Check if fingerprint is banned"""
        # Use the BanManager for consistency and to avoid duplicate code
        from app.ban import BanManager
        ban_status = BanManager.is_banned(fingerprint)
        return ban_status['banned']
    
    @staticmethod
    def get_fingerprint_data(fingerprint):
        """Get fingerprint data from database"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT uuid, fingerprint, hmac_key, created_at, last_seen, visit_count
                FROM fingerprints WHERE fingerprint = ?
            ''', (fingerprint,))
            row = cursor.fetchone()
            
            if row:
                return {
                    'uuid': row[0],
                    'fingerprint': row[1], 
                    'hmac_key': row[2],
                    'created_at': row[3],
                    'last_seen': row[4],
                    'visit_count': row[5]
                }
            return None

class NonceManager:
    """Manages nonces for replay defense"""
    
    @staticmethod
    def generate_nonce():
        """Generate a new nonce"""
        return str(uuid.uuid4())
    
    @staticmethod
    def validate_and_consume_nonce(nonce, fingerprint):
        """Validate nonce and mark as used"""
        if not nonce:
            return False, "Missing nonce"
        
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Check if nonce was already used
            cursor.execute('SELECT 1 FROM used_nonces WHERE nonce = ?', (nonce,))
            if cursor.fetchone():
                return False, "Nonce already used"
            
            # Store nonce as used (expires in 5 minutes)
            expires_at = datetime.now() + timedelta(minutes=5)
            cursor.execute('''
                INSERT INTO used_nonces (nonce, fingerprint, expires_at)
                VALUES (?, ?, ?)
            ''', (nonce, fingerprint, expires_at))
            
            # Clean up expired nonces
            cursor.execute('DELETE FROM used_nonces WHERE expires_at < ?', (datetime.now(),))
            
            conn.commit()
            return True, "Nonce valid"
    
    @staticmethod
    def cleanup_expired_nonces():
        """Clean up expired nonces (called periodically)"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('DELETE FROM used_nonces WHERE expires_at < ?', (datetime.now(),))
            deleted = cursor.rowcount
            conn.commit()
            return deleted

@fp_bp.route('/collect', methods=['POST'])
def collect_fingerprint():
    """
    Collect entropy data and generate server-side fingerprint
    Expects JSON with entropy_data and nonce
    """
    try:
        # Only accept JSON content
        if not request.is_json:
            abort(400, "Content-Type must be application/json")
        
        data = request.get_json()
        if not data:
            abort(400, "Invalid JSON data")
        
        entropy_data = data.get('fb')
        fingerprint_data = data.get('fbdata')
        nonce = data.get('nonce')
        
        if not entropy_data:
            return jsonify({'success': False, 'error': 'Missing entropy data'}), 400
        
        if not fingerprint_data:
            return jsonify({'success': False, 'error': 'Missing fingerprint data'}), 400
        
        if not nonce:
            return jsonify({'success': False, 'error': 'Missing nonce'}), 400
        try:
            entropy_data = decrypt(entropy_data, nonce.split("-")[0])
            fingerprint_data = json.loads(decrypt(fingerprint_data, nonce.split("-")[0]))
            is_valid, message = FingerprintManager.validate_entropy_data(fingerprint_data)
            if not is_valid:
                return jsonify({'success': False, 'error': message}), 400
        except:
            return jsonify({'success': False, 'error': 'Failed to verify the entropy data'}), 400
        
        # Generate server-side fingerprint
        fingerprint = FingerprintManager.generate_server_fingerprint(fingerprint_data)
        
        # Check if fingerprint is banned
        if FingerprintManager.is_fingerprint_banned(fingerprint):
            abort(403, "Access denied")
        
        # Validate and consume nonce
        nonce_valid, nonce_message = NonceManager.validate_and_consume_nonce(nonce, fingerprint)
        if not nonce_valid:
            return jsonify({'success': False, 'error': nonce_message}), 400
        
        # Store fingerprint
        ip_address = get_client_ip()
        user_agent = request.headers.get('User-Agent', '')
        FingerprintManager.store_fingerprint(fingerprint, fingerprint_data, ip_address, user_agent)
        
        # Create response with secure cookies
        response = make_response(jsonify({
            'success': True,
            'fingerprint': encrypt(fingerprint, nonce.split("-")[0])
        }))
        
        # Set fingerprint cookie (HttpOnly, SameSite=Lax, 365 days)
        response.set_cookie(
            'fingerprint',
            fingerprint,
            max_age=365*24*60*60,  # 365 days
            httponly=True,
            samesite='Lax',
            secure=request.is_secure
        )
        
        return response
        
    except Exception as e:
        print(e)
        return jsonify({'success': False, 'error': 'Internal server error'}), 500

@fp_bp.route('/nonce', methods=['GET'])
def get_nonce():
    """Generate a new nonce for client use"""
    try:
        nonce = NonceManager.generate_nonce()
        return jsonify({'success': True, 'nonce': nonce})
    except Exception as e:
        return jsonify({'success': False, 'error': 'Failed to generate nonce'}), 500

def validate_fingerprint_header():
    """
    Middleware function to validate X-Visitor-FP header
    Should be called in before_request hooks
    """
    # Skip validation for certain routes
    skip_routes = ['fp.', 'auth.', 'admin.', 'ban.', 'referral.', 'rating.', 'security.']
    if request.endpoint and any(request.endpoint.startswith(route) for route in skip_routes):
        return None

    # Skip validation for admin users
    if session.get('user_id'):
        return None

    # Skip fingerprint validation for search engine bots
    from app.utils.seo import BotDetector
    from app.utils.common import get_client_ip
    user_agent = request.headers.get('User-Agent', '')
    client_ip = get_client_ip()
    if BotDetector.should_bypass_security(user_agent, client_ip):
        return None

    # Skip for SEO routes (sitemap, robots.txt)
    if request.endpoint in ['main.sitemap_xml', 'main.robots_txt']:
        return None

    # Get fingerprint from cookie and header
    cookie_fp = request.cookies.get('fingerprint')
    header_fp = request.headers.get('X-Visitor-FP')

    # If no fingerprint cookie, let auth system handle it
    if not cookie_fp:
        return None

    # For API routes, validate header matches cookie
    if request.path.startswith('/api/') and header_fp != cookie_fp:
        abort(400, "Invalid fingerprint header")

    # Check if fingerprint is banned
    if FingerprintManager.is_fingerprint_banned(cookie_fp):
        abort(403, "Access denied")

    return None
