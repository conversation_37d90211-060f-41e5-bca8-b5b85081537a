import base64

def encrypt(text: str, key: str) -> str:
    text_bytes = text.encode('utf-8')
    key_bytes = key.encode('utf-8')
    result = bytearray()

    for i in range(len(text_bytes)):
        result.append(text_bytes[i] ^ key_bytes[i % len(key_bytes)])

    return base64.b64encode(result).decode('utf-8')

def decrypt(b64_text: str, key: str) -> str:
    encrypted_bytes = base64.b64decode(b64_text)
    key_bytes = key.encode('utf-8')
    result = bytearray()

    for i in range(len(encrypted_bytes)):
        result.append(encrypted_bytes[i] ^ key_bytes[i % len(key_bytes)])

    return result.decode('utf-8')
