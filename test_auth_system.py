#!/usr/bin/env python3
"""
Test script for the upgraded Flask-Login authentication system
"""
import os
import sys
import requests
import time
from datetime import datetime

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_authentication_system():
    """Test the Flask-Login authentication system"""
    print("🔐 Testing Flask-Login Authentication System")
    print("=" * 50)
    
    # Test configuration
    base_url = "http://localhost:5000"
    test_credentials = {
        'username': '0xmrpepe',  # Default admin user
        'password': 'admin123'
    }
    
    # Create a session to maintain cookies
    session = requests.Session()
    
    try:
        # Test 1: Check if server is running
        print("1. Testing server connectivity...")
        try:
            response = session.get(f"{base_url}/")
            print(f"   ✓ Server is running (Status: {response.status_code})")
        except requests.exceptions.ConnectionError:
            print("   ❌ Server is not running. Please start the Flask app first.")
            return False
        
        # Test 2: Test login page access
        print("\n2. Testing login page access...")
        response = session.get(f"{base_url}/auth/login")
        if response.status_code == 200:
            print("   ✓ Login page accessible")
        else:
            print(f"   ❌ Login page not accessible (Status: {response.status_code})")
            return False
        
        # Test 3: Test protected route without login (should redirect)
        print("\n3. Testing protected route access without login...")
        response = session.get(f"{base_url}/admin/dashboard", allow_redirects=False)
        if response.status_code in [302, 401]:
            print("   ✓ Protected route properly redirects unauthenticated users")
        else:
            print(f"   ❌ Protected route should redirect (Status: {response.status_code})")
        
        # Test 4: Test login functionality
        print("\n4. Testing login functionality...")
        login_data = {
            'username': test_credentials['username'],
            'password': test_credentials['password']
        }
        
        response = session.post(f"{base_url}/auth/login", data=login_data, allow_redirects=True)
        if response.status_code == 200 and 'dashboard' in response.url:
            print("   ✓ Login successful - redirected to dashboard")
        else:
            print(f"   ❌ Login failed (Status: {response.status_code}, URL: {response.url})")
            print(f"   Response content: {response.text[:200]}...")
            return False
        
        # Test 5: Test protected route access after login
        print("\n5. Testing protected route access after login...")
        response = session.get(f"{base_url}/admin/dashboard")
        if response.status_code == 200:
            print("   ✓ Protected route accessible after login")
        else:
            print(f"   ❌ Protected route not accessible after login (Status: {response.status_code})")
        
        # Test 6: Test session persistence
        print("\n6. Testing session persistence...")
        time.sleep(1)  # Wait a moment
        response = session.get(f"{base_url}/admin/dashboard")
        if response.status_code == 200:
            print("   ✓ Session persists across requests")
        else:
            print(f"   ❌ Session not persisting (Status: {response.status_code})")
        
        # Test 7: Test logout functionality
        print("\n7. Testing logout functionality...")
        response = session.get(f"{base_url}/auth/logout", allow_redirects=True)
        if response.status_code == 200:
            print("   ✓ Logout successful")
        else:
            print(f"   ❌ Logout failed (Status: {response.status_code})")
        
        # Test 8: Test protected route access after logout
        print("\n8. Testing protected route access after logout...")
        response = session.get(f"{base_url}/admin/dashboard", allow_redirects=False)
        if response.status_code in [302, 401]:
            print("   ✓ Protected route properly blocks access after logout")
        else:
            print(f"   ❌ Protected route should block access after logout (Status: {response.status_code})")
        
        print("\n" + "=" * 50)
        print("🎉 All authentication tests passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        return False

def test_user_model():
    """Test the User model with Flask-Login integration"""
    print("\n🧪 Testing User Model with Flask-Login")
    print("=" * 50)
    
    try:
        from app.models import User
        
        # Test 1: Test User.get() method
        print("1. Testing User.get() method...")
        user = User.get(1)  # Assuming admin user has ID 1
        if user and hasattr(user, 'id') and hasattr(user, 'username'):
            print(f"   ✓ User.get() works - Found user: {user.username}")
        else:
            print("   ❌ User.get() failed or returned invalid user object")
            return False
        
        # Test 2: Test UserMixin methods
        print("\n2. Testing UserMixin methods...")
        if hasattr(user, 'is_authenticated') and user.is_authenticated:
            print("   ✓ is_authenticated property works")
        else:
            print("   ❌ is_authenticated property failed")
        
        if hasattr(user, 'is_active') and user.is_active:
            print("   ✓ is_active property works")
        else:
            print("   ❌ is_active property failed")
        
        if hasattr(user, 'is_anonymous') and not user.is_anonymous:
            print("   ✓ is_anonymous property works")
        else:
            print("   ❌ is_anonymous property failed")
        
        if hasattr(user, 'get_id') and user.get_id():
            print(f"   ✓ get_id() method works - ID: {user.get_id()}")
        else:
            print("   ❌ get_id() method failed")
        
        # Test 3: Test role methods
        print("\n3. Testing role methods...")
        if hasattr(user, 'is_admin') and callable(user.is_admin):
            print(f"   ✓ is_admin() method works - Is admin: {user.is_admin()}")
        else:
            print("   ❌ is_admin() method failed")
        
        if hasattr(user, 'is_publisher') and callable(user.is_publisher):
            print(f"   ✓ is_publisher() method works - Is publisher: {user.is_publisher()}")
        else:
            print("   ❌ is_publisher() method failed")
        
        print("\n" + "=" * 50)
        print("🎉 User model tests passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ User model test failed with error: {str(e)}")
        return False

if __name__ == "__main__":
    print(f"🚀 Starting Authentication System Tests - {datetime.now()}")
    
    # Test the User model first
    user_test_passed = test_user_model()
    
    # Then test the web authentication
    if user_test_passed:
        auth_test_passed = test_authentication_system()
        
        if auth_test_passed:
            print("\n🎊 ALL TESTS PASSED! Your Flask-Login authentication system is working correctly.")
            sys.exit(0)
        else:
            print("\n💥 Some authentication tests failed. Please check the output above.")
            sys.exit(1)
    else:
        print("\n💥 User model tests failed. Please check the User class implementation.")
        sys.exit(1)
